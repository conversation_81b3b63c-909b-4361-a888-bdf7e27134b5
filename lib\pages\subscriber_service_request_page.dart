import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:geolocator/geolocator.dart';
import '../models/tower_model.dart';
import '../models/service_request_model.dart';

import '../services/tower_service.dart';
import '../services/database_service.dart';
import '../services/map_service.dart';
import '../services/app_settings_service.dart';
import 'package:uuid/uuid.dart';

class SubscriberServiceRequestPage extends StatefulWidget {
  const SubscriberServiceRequestPage({Key? key}) : super(key: key);

  @override
  State<SubscriberServiceRequestPage> createState() => _SubscriberServiceRequestPageState();
}

class _SubscriberServiceRequestPageState extends State<SubscriberServiceRequestPage> {
  final TowerService _towerService = TowerService();
  final DatabaseService _databaseService = DatabaseService();
  final MapService _mapService = MapService();
  
  // متغيرات الخريطة
  final MapController _mapController = MapController();
  LatLng? _currentLocation;
  LatLng? _selectedLocation;
  String _currencySymbol = 'د.ع'; // رمز العملة الافتراضي
  
  // متغيرات النموذج
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  
  // متغيرات البحث عن البرج
  TowerModel? _selectedTower;
  bool _isLoading = false;
  bool _isSearching = false;
  String? _selectedPackage;
  double? _selectedPrice;
  
  // قائمة الأبراج المتاحة
  List<Map<String, dynamic>> _towerResults = [];
  String? _adminId;
  
  // معلومات البحث
  int _totalTowers = 0;
  int _coveringTowers = 0;
  double? _nearestDistance;
  
  // قائمة الباقات المتاحة (من البرج المحدد)
  List<PackagePrice> _availablePackages = [];

  @override
  void initState() {
    super.initState();
    // لا تستدعي _getCurrentLocation هنا
    _loadAdminId();
    _loadPackages();
  }

  @override
  void dispose() {
    _mapController.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _loadAdminId() async {
    try {
      final user = await _databaseService.getCurrentUser();
      if (user != null) {
        setState(() {
          _adminId = user.adminId;
        });
      }
      
      // تحميل إعدادات العملة
      final currencySymbol = await AppSettingsService.getCurrencySymbol();
      setState(() {
        _currencySymbol = currencySymbol;
      });
    } catch (e) {
      // إذا لم يكن هناك مستخدم مسجل دخول، نستخدم معرف افتراضي
      setState(() {
        _adminId = 'default_admin';
      });
    }
  }

  Future<void> _loadPackages() async {
    // سيتم تحميل الباقات عند اختيار البرج
    setState(() {
      _availablePackages = [];
      _selectedPackage = null;
      _selectedPrice = null;
    });
  }

  Future<void> _getCurrentLocation() async {
    setState(() => _isLoading = true);
    
    try {
      final result = await _mapService.getCurrentLocationWithRetry(maxRetries: 3);
      
      if (result['success']) {
        final location = result['location'] as LatLng;
        setState(() {
          _currentLocation = location;
          _selectedLocation = location;
        });
        // لا تستدعي _mapController.move إلا إذا كانت الخريطة معروضة
        // سيتم تحريك الخريطة تلقائياً عند البناء
        await _searchForTowers();
        
        _mapService.showLocationSuccess(context, location);
      } else {
        _mapService.showLocationError(context, result);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في الحصول على الموقع: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _searchForTowers() async {
    if (_selectedLocation == null || _adminId == null) return;
    
    setState(() => _isSearching = true);
    
    try {
      final searchResult = await _towerService.searchNearbyTowers(
        _selectedLocation!.latitude,
        _selectedLocation!.longitude,
        _adminId!,
      );
      
      setState(() {
        _towerResults = List<Map<String, dynamic>>.from(searchResult['towers']);
        _totalTowers = searchResult['totalTowers'];
        _coveringTowers = searchResult['coveringTowers'];
        _nearestDistance = searchResult['nearestDistance'];
        _selectedTower = searchResult['nearestTower'];
      });
      
      if (_towerResults.isNotEmpty) {
        final coveringCount = _towerResults.where((r) => r['isInCoverage'] as bool).length;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم العثور على $_totalTowers برج، $coveringCount برج يغطي موقعك'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا توجد أبراج متاحة في منطقتك'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في البحث عن الأبراج: $e')),
      );
    } finally {
      setState(() => _isSearching = false);
    }
  }

  void _selectTower(TowerModel tower) {
    setState(() {
      _selectedTower = tower;
      // تحميل باقات البرج المحدد
      _availablePackages = tower.packages;
      _selectedPackage = null;
      _selectedPrice = null;
    });
    
    // تحريك الخريطة إلى البرج المحدد
    _mapController.move(LatLng(tower.latitude, tower.longitude), 16);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم اختيار برج: ${tower.name} - ${tower.packages.length} باقة متاحة'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  Future<void> _submitRequest() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedLocation == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى تحديد موقعك على الخريطة')),
      );
      return;
    }
    if (_selectedTower == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار برج من القائمة')),
      );
      return;
    }
    if (_selectedPackage == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار باقة')),
      );
      return;
    }

    try {
      final request = ServiceRequestModel(
        id: const Uuid().v4(),
        subscriberName: _nameController.text,
        subscriberPhone: _phoneController.text,
        subscriberLatitude: _selectedLocation!.latitude,
        subscriberLongitude: _selectedLocation!.longitude,
        towerId: _selectedTower!.id,
        towerName: _selectedTower!.name,
        selectedPackage: _selectedPackage!,
        packagePrice: _selectedPrice!,
        status: RequestStatus.pending,
        adminId: _adminId,
        createdAt: DateTime.now(),
      );

      await _towerService.addServiceRequest(request);
      
      if (!mounted) return;
      
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إرسال طلبك بنجاح! سنتواصل معك قريباً'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في إرسال الطلب: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('طلب خدمة جديدة'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.my_location),
            onPressed: _getCurrentLocation,
            tooltip: 'موقعي الحالي',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : (_selectedLocation == null)
              ? Center(
                  child: Padding(
                    padding: const EdgeInsets.all(32),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.location_on, color: Colors.green, size: 64),
                        const SizedBox(height: 24),
                        const Text(
                          'يرجى السماح للتطبيق بتحديد موقعك لعرض الأبراج القريبة منك',
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton.icon(
                          icon: const Icon(Icons.my_location),
                          label: const Text('تحديد موقعي'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 32),
                          ),
                          onPressed: _getCurrentLocation,
                        ),
                      ],
                    ),
                  ),
                )
              : SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                    // معلومات البحث
                    if (_towerResults.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.all(12),
                        color: Colors.blue.shade50,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.info_outline, color: Colors.blue.shade600, size: 20),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'تم العثور على $_totalTowers برج، $_coveringTowers برج يغطي موقعك',
                                style: TextStyle(
                                  color: Colors.blue.shade700,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            if (_nearestDistance != null)
                              Text(
                                'الأقرب: ${_nearestDistance!.toStringAsFixed(1)} كم',
                                style: TextStyle(
                                  color: Colors.blue.shade700,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                          ],
                        ),
                      ),
                    
                    // الخريطة
                    if (_selectedLocation != null)
                      Container(
                        height: 300,
                        child: FlutterMap(
                          mapController: _mapController,
                          options: MapOptions(
                            initialCenter: _currentLocation ?? const LatLng(24.7136, 46.6753),
                            initialZoom: 15,
                            onTap: (_, location) {
                              setState(() {
                                _selectedLocation = location;
                              });
                              _searchForTowers();
                            },
                            onMapReady: () {
                              // عند جاهزية الخريطة، حركها إلى الموقع الحالي
                              if (_currentLocation != null) {
                                _mapController.move(_currentLocation!, 15);
                              }
                            },
                          ),
                          children: [
                            TileLayer(
                              urlTemplate: 'https://cartodb-basemaps-{s}.global.ssl.fastly.net/light_all/{z}/{x}/{y}.png',
                              subdomains: const ['a', 'b', 'c', 'd'],
                              userAgentPackageName: 'com.example.isp_manager',
                            ),
                            
                            // رسم جميع الأبراج
                            ...(_towerResults.map((result) {
                              final tower = result['tower'] as TowerModel;
                              final distance = result['distance'] as double;
                              final isInCoverage = result['isInCoverage'] as bool;
                              final isSelected = _selectedTower?.id == tower.id;
                              
                              return [
                                // دائرة التغطية
                                CircleLayer(
                                  circles: [
                                    CircleMarker(
                                      point: LatLng(tower.latitude, tower.longitude),
                                      radius: tower.coverageRadius * 1000,
                                      color: isInCoverage 
                                          ? Colors.green.withOpacity(0.2)
                                          : Colors.grey.withOpacity(0.1),
                                      borderColor: isInCoverage ? Colors.green : Colors.grey,
                                      borderStrokeWidth: isSelected ? 3 : 1,
                                    ),
                                  ],
                                ),
                                
                                // علامة البرج
                                MarkerLayer(
                                  markers: [
                                    Marker(
                                      point: LatLng(tower.latitude, tower.longitude),
                                      width: isSelected ? 40 : 30,
                                      height: isSelected ? 40 : 30,
                                      child: GestureDetector(
                                        onTap: () => _selectTower(tower),
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: isInCoverage ? Colors.green : Colors.grey,
                                            shape: BoxShape.circle,
                                            border: Border.all(
                                              color: isSelected ? Colors.blue : Colors.white,
                                              width: isSelected ? 3 : 2,
                                            ),
                                          ),
                                          child: Icon(
                                            Icons.cell_tower,
                                            color: Colors.white,
                                            size: isSelected ? 20 : 16,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ];
                            }).expand((i) => i)),
                            
                            // رسم الموقع المحدد
                            if (_selectedLocation != null)
                              MarkerLayer(
                                markers: [
                                  Marker(
                                    point: _selectedLocation!,
                                    width: 40,
                                    height: 40,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: Colors.red,
                                        shape: BoxShape.circle,
                                        border: Border.all(color: Colors.white, width: 2),
                                      ),
                                      child: const Icon(
                                        Icons.location_on,
                                        color: Colors.white,
                                        size: 20,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                          ],
                        ),
                      ),
                    
                    // قائمة الأبراج
                    if (_towerResults.isNotEmpty)
                      Container(
                        height: 180,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          border: Border(top: BorderSide(color: Colors.grey.shade300)),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.cell_tower, color: Colors.green.shade600, size: 20),
                                const SizedBox(width: 8),
                                Text(
                                  'الأبراج المتاحة',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const Spacer(),
                                if (_isSearching)
                                  const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Expanded(
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: _towerResults.length,
                                itemBuilder: (context, index) {
                                  final result = _towerResults[index];
                                  final tower = result['tower'] as TowerModel;
                                  final distance = result['distance'] as double;
                                  final isInCoverage = result['isInCoverage'] as bool;
                                  final isSelected = _selectedTower?.id == tower.id;
                                  
                                  return Container(
                                    width: 200,
                                    margin: const EdgeInsets.only(right: 12),
                                    child: Card(
                                      elevation: isSelected ? 4 : 2,
                                      color: isSelected ? Colors.blue.shade50 : Colors.white,
                                      child: InkWell(
                                        onTap: () => _selectTower(tower),
                                        borderRadius: BorderRadius.circular(8),
                                        child: Padding(
                                          padding: const EdgeInsets.all(12),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.min,
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Icon(
                                                    Icons.cell_tower,
                                                    color: isInCoverage ? Colors.green : Colors.grey,
                                                    size: 16,
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Expanded(
                                                    child: Text(
                                                      tower.name,
                                                      style: TextStyle(
                                                        fontWeight: FontWeight.bold,
                                                        color: isSelected ? Colors.blue.shade700 : null,
                                                      ),
                                                      maxLines: 1,
                                                      overflow: TextOverflow.ellipsis,
                                                    ),
                                                  ),
                                                  if (isSelected)
                                                    Icon(Icons.check_circle, color: Colors.blue, size: 16),
                                                ],
                                              ),
                                              const SizedBox(height: 4),
                                              Text(
                                                '${distance.toStringAsFixed(1)} كم',
                                                style: TextStyle(
                                                  fontSize: 11,
                                                  color: Colors.grey.shade600,
                                                ),
                                              ),
                                              Container(
                                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                                decoration: BoxDecoration(
                                                  color: isInCoverage ? Colors.green.shade100 : Colors.grey.shade100,
                                                  borderRadius: BorderRadius.circular(4),
                                                ),
                                                child: Text(
                                                  isInCoverage ? 'يغطي موقعك' : 'خارج التغطية',
                                                  style: TextStyle(
                                                    fontSize: 10,
                                                    color: isInCoverage ? Colors.green.shade700 : Colors.grey.shade600,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    
                    // نموذج الطلب
                    Container(
                      padding: const EdgeInsets.all(16),
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.shade300,
                            blurRadius: 4,
                            offset: const Offset(0, -2),
                          ),
                        ],
                      ),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'معلومات الطلب',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 12),
                            TextFormField(
                              controller: _nameController,
                              decoration: const InputDecoration(
                                labelText: 'الاسم الكامل',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.person),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'يرجى إدخال الاسم';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 12),
                            TextFormField(
                              controller: _phoneController,
                              decoration: const InputDecoration(
                                labelText: 'رقم الهاتف',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.phone),
                              ),
                              keyboardType: TextInputType.phone,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'يرجى إدخال رقم الهاتف';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 12),
                            DropdownButtonFormField<String>(
                              value: _selectedPackage,
                              decoration: InputDecoration(
                                labelText: _selectedTower == null ? 'اختر برج أولاً' : 'اختر الباقة',
                                border: const OutlineInputBorder(),
                                prefixIcon: const Icon(Icons.inventory),
                                enabled: _selectedTower != null,
                              ),
                              items: _availablePackages.isEmpty 
                                ? [
                                    const DropdownMenuItem<String>(
                                      value: null,
                                      child: Text('لا توجد باقات متاحة'),
                                    ),
                                  ]
                                : _availablePackages.map((package) {
                                    return DropdownMenuItem<String>(
                                      value: package.name,
                                      child: Text('${package.name} - ${package.price.toStringAsFixed(0)} $_currencySymbol'),
                                    );
                                  }).toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  final selectedPackage = _availablePackages.firstWhere(
                                    (package) => package.name == value,
                                  );
                                  setState(() {
                                    _selectedPackage = value;
                                    _selectedPrice = selectedPackage.price;
                                  });
                                }
                              },
                              validator: (value) {
                                if (value == null) {
                                  return 'يرجى اختيار باقة';
                                }
                                return null;
                              },
                            ),
                            
                            // عرض تفاصيل الباقة المختارة
                            if (_selectedPackage != null && _availablePackages.isNotEmpty)
                              Container(
                                margin: const EdgeInsets.only(top: 12),
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.blue.shade50,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: Colors.blue.shade200),
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'تفاصيل الباقة المختارة:',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.blue.shade700,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Builder(
                                      builder: (context) {
                                        try {
                                          final selectedPackageData = _availablePackages.firstWhere(
                                            (package) => package.name == _selectedPackage,
                                          );
                                          return Column(
                                            mainAxisSize: MainAxisSize.min,
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text('السعر: ${selectedPackageData.price.toStringAsFixed(0)} $_currencySymbol'),
                                              if (selectedPackageData.description.isNotEmpty)
                                                Text('الوصف: ${selectedPackageData.description}'),
                                            ],
                                          );
                                        } catch (e) {
                                          return const Text('معلومات الباقة غير متوفرة');
                                        }
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            
                            const SizedBox(height: 12),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                onPressed: _submitRequest,
                                icon: const Icon(Icons.send),
                                label: const Text('إرسال الطلب'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.green,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
    );
  }
} 