import 'dart:async';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:isp_manager/models/ai_assistant_model.dart';
import 'package:isp_manager/models/mikrotik_device_model.dart';
import 'package:isp_manager/services/gemini_service.dart';
import 'package:isp_manager/services/mikrotik_service.dart';
import 'package:isp_manager/services/ssh_service.dart';

class AIAssistantPage extends StatefulWidget {
  const AIAssistantPage({Key? key}) : super(key: key);

  @override
  _AIAssistantPageState createState() => _AIAssistantPageState();
}

class _AIAssistantPageState extends State<AIAssistantPage> {
  // Controllers
  final TextEditingController _messageController = TextEditingController();
  final TextEditingController _apiKeyController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // Services
  GeminiService _geminiService = GeminiService(apiKey: '');
  final MikrotikService _mikrotikService = MikrotikService();
  final SshService _sshService = SshService();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // State variables
  List<AIChatMessage> _messages = [];
  List<MikrotikDeviceModel> _devices = [];
  List<MikrotikDeviceModel> _filteredDevices = [];
  bool _isLoading = false;
  bool _isSearching = false;
  bool _showApiKeyInput = false;
  String? _savedApiKey;
  String? _userId;

  @override
  void initState() {
    super.initState();
    _userId = FirebaseAuth.instance.currentUser?.uid;
    _loadApiKey();
    _loadDevices();
    _addSystemMessage(
        'مرحباً! أنا مساعدك الذكي لإدارة أجهزة الشبكة. كيف يمكنني مساعدتك اليوم؟');
  }

  @override
  void dispose() {
    _messageController.dispose();
    _searchController.dispose();
    _scrollController.dispose();
    _sshService.dispose();
    super.dispose();
  }

  // Load devices from Firestore
  Future<void> _loadDevices() async {
    if (_userId == null) return;

    try {
      final snapshot = await _firestore
          .collection('mikrotik_devices')
          .where('adminId', isEqualTo: _userId)
          .get();

      setState(() {
        _devices = snapshot.docs
            .map((doc) => MikrotikDeviceModel.fromMap(doc.data()))
            .toList();
        _filteredDevices = List.from(_devices);
      });
    } catch (e) {
      _addSystemMessage('حدث خطأ أثناء تحميل الأجهزة: ${e.toString()}');
    }
  }

  // Filter devices based on search query
  void _filterDevices(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredDevices = List.from(_devices);
      });
      return;
    }

    final lowerQuery = query.toLowerCase();
    setState(() {
      _filteredDevices = _devices.where((device) {
        return device.name.toLowerCase().contains(lowerQuery) ||
            (device.host).toLowerCase().contains(lowerQuery) ||
            (device.id ?? '').toLowerCase().contains(lowerQuery);
      }).toList();
    });
  }

  // Load API key from Firestore
  Future<void> _loadApiKey() async {
    if (_userId == null) return;

    try {
      final doc = await _firestore
          .collection('ai_assistant_configs')
          .doc(_userId)
          .get();

      if (doc.exists) {
        setState(() {
          _savedApiKey = doc.data()?['geminiApiKey'];
          if (_savedApiKey != null) {
            _geminiService = GeminiService(apiKey: _savedApiKey!);
          } else {
            _showApiKeyInput = true;
          }
        });
      } else {
        setState(() {
          _showApiKeyInput = true;
        });
      }
    } catch (e) {
      debugPrint('Error loading API key: $e');
      _showError('حدث خطأ أثناء تحميل إعدادات المساعد');
    }
  }

  // Save API key to Firestore
  Future<void> _saveApiKey() async {
    if (_apiKeyController.text.isEmpty || _userId == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await _firestore
          .collection('ai_assistant_configs')
          .doc(_userId)
          .set({
        'geminiApiKey': _apiKeyController.text,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      setState(() {
        _savedApiKey = _apiKeyController.text;
        _geminiService = GeminiService(apiKey: _savedApiKey!);
        _showApiKeyInput = false;
        _addSystemMessage('تم حفظ مفتاح API بنجاح! كيف يمكنني مساعدتك اليوم؟');
      });
    } catch (e) {
      debugPrint('Error saving API key: $e');
      _showError('حدث خطأ أثناء حفظ مفتاح API');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Add system message to chat
  void _addSystemMessage(String text) {
    setState(() {
      _messages.add(AIChatMessage.assistant(content: text));
    });
    _scrollToBottom();
  }

  // Show error message
  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  // Scroll to bottom of chat
  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  // Send message to Gemini API
  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    // Check if the message is about a specific device
    MikrotikDeviceModel? selectedDevice;
    for (var device in _devices) {
      if (message.contains(device.name) ||
          (device.host.isNotEmpty && message.contains(device.host)) ||
          (device.id != null && message.contains(device.id!))) {
        selectedDevice = device;
        break;
      }
    }

    // Create user message with device context
    final userMessage = AIChatMessage.user(
      content: message,
      device: selectedDevice,
    );

    setState(() {
      _messages.add(userMessage);
      _messageController.clear();
      _isLoading = true;
      _isSearching = false;
    });

    _scrollToBottom();

    try {
      String response;
      
      // If a device is mentioned, prepare device context
      String deviceContext = '';
      if (selectedDevice != null) {
        deviceContext = "\n\nمعلومات الجهاز المستهدف:\n"
            "الاسم: ${selectedDevice.name}\n"
            "العنوان: ${selectedDevice.host}:${selectedDevice.port}\n"
            "اسم المستخدم: ${selectedDevice.username}\n";
        
        // Try to get basic device info to verify connection
        try {
          final deviceInfo = await _sshService.connectAndExecute(
            selectedDevice, 
            '/system resource print'
          );
          deviceContext += '\nمعلومات النظام:\n$deviceInfo\n';
        } catch (e) {
          print('Failed to get device info: $e');
        }
      }

      // Check if this is a direct command (starts with /)
      if (message.trim().startsWith('/')) {
        if (selectedDevice != null) {
          final command = message.trim();
          final result = await _sshService.connectAndExecute(selectedDevice, command);
          response = 'نتيجة الأمر:\n$result';
        } else {
          response = 'الرجاء تحديد جهاز لتنفيذ الأمر عليه';
        }
      } 
      // Otherwise, use Gemini for AI response
      else {
        final prompt = '$message$deviceContext';
        response = await _geminiService.generateContent(
          prompt,
          chatHistory: _messages.map((m) => m.toMap()).toList(),
        );
      }

      setState(() {
        _messages.add(
          AIChatMessage.assistant(
            content: response,
            device: selectedDevice,
          ),
        );
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _messages.add(
          AIChatMessage.assistant(
            content: 'عذراً، حدث خطأ: ${e.toString()}\n\n' 
                     'تأكد من صحة بيانات الاتصال بالجهاز وأنه متصل بالشبكة.',
            device: selectedDevice,
          ),
        );
        _isLoading = false;
      });
    }

    _scrollToBottom();
  }

  // Build the chat message widget
  Widget _buildMessage(AIChatMessage message) {
    final isUser = message.role == 'user';

    return Align(
      alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: isUser
              ? Theme.of(context).primaryColor.withOpacity(0.1)
              : Theme.of(context).colorScheme.surfaceVariant,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment:
              isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
          children: [
            if (message.device != null) _buildDeviceCard(message.device!),
            Text(
              message.content,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: isUser ? TextAlign.right : TextAlign.left,
            ),
            const SizedBox(height: 4),
            Text(
              _formatTime(message.timestamp),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context)
                        .textTheme
                        .bodySmall
                        ?.color
                        ?.withOpacity(0.6),
                  ),
            ),
          ],
        ),
      ),
    );
  }

  // Format time to HH:MM format
  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  // Build device card widget
  Widget _buildDeviceCard(MikrotikDeviceModel device) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.router, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              Text(
                device.name,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          _buildDetailRow('العنوان', '${device.host}:${device.port}'),
          _buildDetailRow('اسم المستخدم', device.username),
        ],
      ),
    );
  }

  // Build a detail row with label and value
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Text(value),
        ],
      ),
    );
  }

  // Build the input area with text field and send button
  Widget _buildInputArea() {
    if (_showApiKeyInput) {
      return _buildApiKeyInputWidget();
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            offset: const Offset(0, -1),
            blurRadius: 4,
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: 'اكتب رسالتك...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surfaceVariant,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.search),
                  onPressed: () {
                    setState(() {
                      _isSearching = !_isSearching;
                      if (!_isSearching) {
                        _searchController.clear();
                        _filterDevices('');
                      }
                    });
                  },
                ),
              ),
              maxLines: 5,
              minLines: 1,
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          const SizedBox(width: 8),
          CircleAvatar(
            backgroundColor: Theme.of(context).primaryColor,
            child: IconButton(
              icon: const Icon(Icons.send, color: Colors.white),
              onPressed: _sendMessage,
            ),
          ),
        ],
      ),
    );
  }

  // Build the API key input widget
  Widget _buildApiKeyInputWidget() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        border: Border(bottom: BorderSide(color: Colors.orange[200]!)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إعدادات Google Gemini API',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          const SizedBox(height: 8),
          TextField(
            controller: _apiKeyController..text = _savedApiKey ?? '',
            decoration: const InputDecoration(
              labelText: 'مفتاح Google Gemini API',
              hintText: 'أدخل مفتاح API الخاص بك',
              border: OutlineInputBorder(),
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            obscureText: true,
          ),
          const SizedBox(height: 8),
          const Text(
            'احصل على مفتاح API من: https://aistudio.google.com/app/apikey',
            style: TextStyle(fontSize: 12, color: Colors.grey),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {
                  setState(() {
                    _showApiKeyInput = false;
                  });
                },
                child: const Text('إلغاء'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: _saveApiKey,
                child: const Text('حفظ'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Clear chat history
  void _clearChat() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح المحادثة'),
        content: const Text('هل أنت متأكد من أنك تريد مسح سجل المحادثة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _messages.clear();
                _addSystemMessage('مرحباً! كيف يمكنني مساعدتك اليوم؟');
              });
              Navigator.pop(context);
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المساعد الذكي'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                  _filterDevices('');
                }
              });
            },
            tooltip: 'بحث عن أجهزة',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'settings') {
                setState(() {
                  _showApiKeyInput = !_showApiKeyInput;
                });
              } else if (value == 'clear') {
                _clearChat();
              }
            },
            itemBuilder: (BuildContext context) => [
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings, size: 20),
                    SizedBox(width: 8),
                    Text('إعدادات API'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'clear',
                child: Row(
                  children: [
                    Icon(Icons.delete_outline, size: 20),
                    SizedBox(width: 8),
                    Text('مسح المحادثة'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          if (_isSearching)
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    offset: const Offset(0, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'ابحث عن جهاز...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(24),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surface,
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                onChanged: _filterDevices,
              ),
            ),
          Expanded(
            child: _messages.isEmpty && !_showApiKeyInput
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.smart_toy,
                            size: 64, color: Colors.blue),
                        const SizedBox(height: 16),
                        Text(
                          'كيف يمكنني مساعدتك اليوم؟',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 8),
                        const Text(
                            'اطرح علي سؤالك وسأساعدك في إدارة أجهزة الشبكة'),
                      ],
                    ),
                  )
                : ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(8),
                    itemCount: _messages.length,
                    itemBuilder: (context, index) {
                      return _buildMessage(_messages[index]);
                    },
                  ),
          ),
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(8.0),
              child: CircularProgressIndicator(),
            ),
          _buildInputArea(),
        ],
      ),
    );
  }
}
