# دليل حل مشاكل ZainCash

## المشكلة الحالية: "الرمز غير صحيح"

### الأسباب المحتملة:

#### 1. **مشكلة في JWT Token**
- **السبب**: طريقة إنشاء JWT token غير متوافقة مع ZainCash
- **الحل**: تم تحديث دالة `_createJwtToken` لتتوافق مع المواصفات

#### 2. **مشكلة في تنسيق البيانات**
- **السبب**: نوع البيانات المرسلة (رقم الهاتف كـ int بدلاً من string)
- **الحل**: تم تغيير `msisdn` ليكون string

#### 3. **مشكلة في Secret Key**
- **السبب**: Secret key غير صحيح أو تالف
- **التحقق**: استخدم صفحة الاختبار للتحقق

## خطوات التشخيص:

### 1. **استخدام صفحة الاختبار**
```dart
// في التطبيق، اذهب إلى:
// صفحة تجديد الاشتراك → أيقونة Bug → اختبار ZainCash
```

### 2. **فحص Logs**
ابحث عن هذه الرسائل في console:
```
✅ تم تكوين بيانات الإنتاج الحقيقية بنجاح
ZainCash: JWT Token: [token]
ZainCash: Payload: [payload]
ZainCash: Response body: {"err":{"msg":"الرمز غير صحيح"}}
```

### 3. **التحقق من البيانات**
```dart
Merchant ID: 5eba52ff3924b2df06877ddc
Secret: $2y$10$dozRcLK6VKjL7L19uXhVdeH7hP/RF65vNeL9w/tC/Am073TaBBwey
MSISDN: 9647819597948
```

## الحلول المطبقة:

### ✅ **تحديث JWT Token**
- إزالة padding من base64url encoding
- إضافة تفاصيل debug للتشخيص
- تحسين معالجة الأخطاء

### ✅ **تصحيح تنسيق البيانات**
- تغيير `msisdn` من int إلى string
- التأكد من تنسيق JSON الصحيح

### ✅ **إضافة أدوات التشخيص**
- صفحة اختبار ZainCash
- logs مفصلة للتشخيص
- معالجة أخطاء محسنة

## خطوات الاختبار:

### 1. **اختبار JWT Token**
```dart
// استخدم صفحة الاختبار لإنشاء token تجريبي
final token = zainCashService.testJwtToken();
```

### 2. **اختبار طلب دفع**
```dart
// اختبر بمبلغ صغير (1000 دينار)
final result = await zainCashService.createPaymentRequest(
  packageId: 'test',
  accountNumber: 'test',
  amount: 1000.0,
  packageName: 'اختبار',
  durationDays: 30,
);
```

### 3. **فحص الاستجابة**
- إذا كانت الاستجابة تحتوي على `{"err": {"msg": "..."}}`
- فهذا يعني أن ZainCash رفض الطلب
- تحقق من صحة البيانات والتوكن

## مشاكل شائعة وحلولها:

### ❌ **"الرمز غير صحيح"**
- **السبب**: JWT token غير صحيح
- **الحل**: تحقق من Secret key وطريقة التشفير

### ❌ **"بيانات الاعتماد غير صحيحة"**
- **السبب**: Merchant ID أو Secret خطأ
- **الحل**: تحقق من البيانات في `ZainCashConfig`

### ❌ **"المبلغ غير صحيح"**
- **السبب**: المبلغ أقل من 250 دينار
- **الحل**: استخدم مبلغ 250 دينار أو أكثر

### ❌ **"خطأ في الاتصال"**
- **السبب**: مشكلة في الشبكة أو URL
- **الحل**: تحقق من الاتصال بالإنترنت

## معلومات إضافية:

### **URLs المستخدمة:**
- Test: `https://test.zaincash.iq`
- Live: `https://api.zaincash.iq`

### **Endpoints:**
- Init: `/transaction/init`
- Get: `/transaction/get`
- Pay: `/transaction/pay`

### **Content-Type:**
```
application/x-www-form-urlencoded
```

### **JWT Structure:**
```
Header.Payload.Signature
```

## التواصل مع ZainCash:

إذا استمرت المشكلة:
1. تأكد من صحة بيانات الاعتماد
2. اتصل بدعم ZainCash: `<EMAIL>`
3. أرسل لهم JWT token المُنشأ للفحص

## ملاحظات مهمة:

⚠️ **لا تشارك Secret key مع أي شخص**
⚠️ **استخدم بيئة الاختبار أولاً**
⚠️ **احفظ logs للمراجعة**

---

**آخر تحديث**: تم إضافة تشخيص مفصل وصفحة اختبار
