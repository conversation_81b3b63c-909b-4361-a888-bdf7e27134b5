import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:isp_manager/models/mikrotik_device_model.dart';
import 'package:isp_manager/services/database_service.dart';
import 'package:isp_manager/services/sqlite_service.dart';
import 'package:isp_manager/services/mikrotik_service.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:sqflite/sqflite.dart';
import 'package:fl_chart/fl_chart.dart';
import 'dart:async';
import 'dart:math' as math;

class MikrotikSettingsPage extends StatefulWidget {
  const MikrotikSettingsPage({super.key});

  @override
  State<MikrotikSettingsPage> createState() => _MikrotikSettingsPageState();
}

class _MikrotikSettingsPageState extends State<MikrotikSettingsPage>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _hostController = TextEditingController();
  final TextEditingController _portController = TextEditingController();
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  MikrotikDeviceModel? _selectedDevice;
  List<MikrotikDeviceModel> _allDevices = [];
  bool _isLoading = false;
  bool _showForm = false;
  Map<String, dynamic>? _deviceDetails;
  final MikrotikService _mikrotikService = MikrotikService();
  bool _disposed = false;
  // New variables for advanced monitoring
  Timer? _monitoringTimer;
  final List<FlSpot> _cpuUsageData = [];
  final List<FlSpot> _memoryUsageData = [];
  final Map<String, List<FlSpot>> _interfaceTrafficData =
      {}; // Stores historical traffic for charts
  int _dataPointCounter = 0;
  bool _isMonitoring = false;

  // Custom command variables
  final TextEditingController _commandController = TextEditingController();
  String _commandResult = '';
  final List<String> _commandHistory = [];
  bool _useSSHForCommand = false;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadData();
    _mikrotikService.isConnected.addListener(_updateConnectionStatus);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _disposed = true;
    _monitoringTimer?.cancel();
    _mikrotikService.isConnected.removeListener(_updateConnectionStatus);
    _nameController.dispose();
    _hostController.dispose();
    _portController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _commandController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    _allDevices = await _loadAllMikrotikDevicesFire();
    if (_allDevices.isEmpty) {
      _loadAllMikrotikDevices();
    }
    syncMicToFirebase();
  }

  void _updateConnectionStatus() {
    if (!_disposed && mounted) {
      // Check if widget is not disposed and still mounted
      setState(() {
        // Rebuild the widget to reflect connection status changes
      });
    }
  }

  Future<void> _loadAllMikrotikDevices() async {
    if (_disposed) return; // Exit early if disposed
    setState(() {
      _isLoading = true;
    });
    final db = await SQLiteService().database;
    final List<Map<String, dynamic>> maps = await db.query('mikrotik_devices');
    if (!_disposed && mounted) {
      // Check before calling setState
      setState(() {
        _allDevices = maps.map((e) => MikrotikDeviceModel.fromMap(e)).toList();
        _isLoading = false;
      });
    }
  }

  Future<List<MikrotikDeviceModel>> _loadAllMikrotikDevicesFire() async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      Fluttertoast.showToast(msg: 'يجب تسجيل الدخول أولاً');
      return [];
    }
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('mikrotik_devices')
          .where('adminId', isEqualTo: currentUser.uid)
          .get();
          
      return snapshot.docs.map((doc) {
        return MikrotikDeviceModel.fromMap(doc.data()..['id'] = doc.id);
      }).toList();
    } catch (e) {
      print('Error fetching MikroTik devices from Firestore: $e');
      Fluttertoast.showToast(msg: 'حدث خطأ أثناء جلب أجهزة المايكروتك');
      return [];
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> syncMicToFirebase() async {
    if (_disposed) return; // Exit early if disposed
    
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      Fluttertoast.showToast(msg: 'يجب تسجيل الدخول أولاً');
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final db = await SQLiteService().database;
      final List<Map<String, dynamic>> maps = await db.query('mikrotik_devices');
      final local = maps.map((e) => MikrotikDeviceModel.fromMap(e)).toList();
      
      final firebaseSnapshot = await FirebaseFirestore.instance
          .collection('mikrotik_devices')
          .where('adminId', isEqualTo: currentUser.uid)
          .get();

      // IDs الموجودة بالفعل في Firestore
      final firebaseIds = firebaseSnapshot.docs.map((doc) => doc.id).toSet();

      for (final mic in local) {
        if (!firebaseIds.contains(mic.id)) {
          final newDevice = mic.copyWith(
            adminId: currentUser.uid,
          );
          await FirebaseFirestore.instance
              .collection('mikrotik_devices')
              .doc(newDevice.id)
              .set(newDevice.toMap());
        }
      }
      
      Fluttertoast.showToast(msg: 'تم مزامنة أجهزة المايكروتك مع Firebase بنجاح');
    } catch (e) {
      print('Error syncing MikroTik devices to Firebase: $e');
      Fluttertoast.showToast(msg: 'حدث خطأ أثناء مزامنة الأجهزة: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _selectDevice(MikrotikDeviceModel device) {
    if (!_disposed && mounted) {
      setState(() {
        _selectedDevice = device;
        _nameController.text = device.name;
        _hostController.text = device.host ?? "";
        _portController.text = device.port.toString();
        _usernameController.text = device.username;
        _passwordController.text = device.password;
        _showForm = true;
      });
      _fetchDeviceDetails(device); // Fetch details when device is selected
    }
  }

  Future<void> _fetchDeviceDetails(MikrotikDeviceModel device) async {
    if (_disposed) return;
    if (mounted) {
      setState(() {
        _isLoading = true;
        _deviceDetails = null;
      });
    }
    try {
      final details = await _mikrotikService.getMikrotikDeviceDetails(device);
      final trafficDetails = await _mikrotikService.getInterfaceTraffic(device);

      if (!_disposed && mounted) {
        setState(() {
          _deviceDetails = {
            ...details,
            'traffic_data': trafficDetails['traffic_data'] ?? {},
            'last_updated': DateTime.now().toIso8601String(),
          };
        });
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'خطأ أثناء جلب تفاصيل الجهاز: $e');
      if (!_disposed && mounted) {
        setState(() {
          _deviceDetails = {
            'error': 'Failed to load details',
            'message': e.toString(),
          };
        });
      }
    } finally {
      if (!_disposed && mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // New methods for advanced monitoring and commands
  void _startRealTimeMonitoring() {
    if (_selectedDevice == null || _isMonitoring) return;

    setState(() {
      _isMonitoring = true;
      _cpuUsageData.clear();
      _memoryUsageData.clear();
      _interfaceTrafficData.clear();
      _dataPointCounter = 0;
    });

    _monitoringTimer = Timer.periodic(const Duration(seconds: 5), (
      timer,
    ) async {
      if (_disposed || !mounted) {
        timer.cancel();
        return;
      }

      await _collectMonitoringData();
    });
  }

  void _stopRealTimeMonitoring() {
    _monitoringTimer?.cancel();
    setState(() {
      _isMonitoring = false;
    });
  }

  Future<void> _collectMonitoringData() async {
    if (_selectedDevice == null) return;

    try {
      final details = await _mikrotikService.getMikrotikDeviceDetails(
        _selectedDevice!,
      );
      final trafficDetails = await _mikrotikService.getInterfaceTraffic(
        _selectedDevice!,
      );

      if (details['status'] == 'success' &&
          trafficDetails['status'] == 'success') {
        _dataPointCounter++;

        // Parse CPU usage
        String cpuLoad = details['cpu_load'] ?? '0%';
        double cpuValue = double.tryParse(cpuLoad.replaceAll('%', '')) ?? 0;

        // Parse memory usage
        String totalMem = details['total_memory'] ?? '0';
        String freeMem = details['free_memory'] ?? '0';
        double totalMemBytes = _parseMemoryValue(totalMem);
        double freeMemBytes = _parseMemoryValue(freeMem);
        double memoryUsage = totalMemBytes > 0
            ? ((totalMemBytes - freeMemBytes) / totalMemBytes) * 100
            : 0;

        if (mounted) {
          setState(() {
            _cpuUsageData.add(FlSpot(_dataPointCounter.toDouble(), cpuValue));
            _memoryUsageData.add(
              FlSpot(_dataPointCounter.toDouble(), memoryUsage),
            );

            // Update interface traffic data
            Map<String, dynamic> currentTraffic =
                trafficDetails['traffic_data'] ?? {};
            currentTraffic.forEach((interfaceName, data) {
              // Asegurarnos de que tenemos valores numéricos válidos
              int rxBytes = 0;
              int txBytes = 0;

              try {
                rxBytes = data['rx_bytes'] is int
                    ? data['rx_bytes']
                    : (int.tryParse(data['rx_bytes'].toString()) ?? 0);
                txBytes = data['tx_bytes'] is int
                    ? data['tx_bytes']
                    : (int.tryParse(data['tx_bytes'].toString()) ?? 0);
              } catch (e) {
                // Si hay algún error en la conversión, usar valores predeterminados
                print(
                  'Error convirtiendo datos de tráfico para $interfaceName: $e',
                );
              }

              // Asegurarnos de que los valores son positivos
              rxBytes = rxBytes.abs();
              txBytes = txBytes.abs();

              // Store RX and TX separately for each interface
              if (!_interfaceTrafficData.containsKey('${interfaceName}_rx')) {
                _interfaceTrafficData['${interfaceName}_rx'] = [];
              }
              if (!_interfaceTrafficData.containsKey('${interfaceName}_tx')) {
                _interfaceTrafficData['${interfaceName}_tx'] = [];
              }

              // Store raw byte counts - we'll convert to Mbps when displaying
              _interfaceTrafficData['${interfaceName}_rx']!.add(
                FlSpot(_dataPointCounter.toDouble(), rxBytes.toDouble()),
              );
              _interfaceTrafficData['${interfaceName}_tx']!.add(
                FlSpot(_dataPointCounter.toDouble(), txBytes.toDouble()),
              );
            }); // Keep only last 30 data points for performance and usability
            const int maxDataPoints = 30;

            if (_cpuUsageData.length > maxDataPoints) {
              _cpuUsageData.removeAt(0);
            }

            if (_memoryUsageData.length > maxDataPoints) {
              _memoryUsageData.removeAt(0);
            }

            // Limpiar cada lista de datos de interfaz por separado
            _interfaceTrafficData.forEach((key, value) {
              if (value.length > maxDataPoints) {
                value.removeAt(0);
              }
            });
          });
        }
      }
    } catch (e) {
      print('Error collecting monitoring data: $e');
    }
  }

  double _parseMemoryValue(String memoryStr) {
    // Parse memory values like "256MiB", "1GiB", etc.
    String cleanStr = memoryStr.replaceAll(RegExp(r'[^0-9.]'), '');
    double value = double.tryParse(cleanStr) ?? 0;

    if (memoryStr.contains('GiB') || memoryStr.contains('GB')) {
      return value * 1024 * 1024 * 1024;
    } else if (memoryStr.contains('MiB') || memoryStr.contains('MB')) {
      return value * 1024 * 1024;
    } else if (memoryStr.contains('KiB') || memoryStr.contains('KB')) {
      return value * 1024;
    }
    return value;
  }

  Future<void> _executeCustomCommand(String command) async {
    if (_selectedDevice == null || command.trim().isEmpty) return;
    setState(() {
      _isLoading = true;
      _commandResult = 'جاري تنفيذ الأمر...';
    });
    try {
      String result;
      if (_useSSHForCommand) {
        result = await _mikrotikService.runCustomSSHCommand(
          _selectedDevice!,
          command,
        );
      } else {
        result = await _mikrotikService.executeCustomCommand(
          _selectedDevice!,
          command,
        );
      }
      _commandHistory.insert(0, command);
      if (_commandHistory.length > 10) {
        _commandHistory.removeLast();
      }
      setState(() {
        _commandResult = result;
        _commandController.clear();
      });
    } catch (e) {
      setState(() {
        _commandResult = 'خطأ في تنفيذ الأمر: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _rebootDevice() async {
    if (_selectedDevice == null) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد إعادة التشغيل'),
        content: Text(
          'هل أنت متأكد من إعادة تشغيل جهاز "${_selectedDevice!.name}"؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('إعادة تشغيل'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _mikrotikService.executeCustomCommand(
          _selectedDevice!,
          '/system/reboot',
        );
        Fluttertoast.showToast(msg: 'تم إرسال أمر إعادة التشغيل بنجاح');
      } catch (e) {
        Fluttertoast.showToast(msg: 'خطأ في إعادة التشغيل: $e');
      }
    }
  }

  void _clearForm() {
    setState(() {
      _selectedDevice = null;
      _nameController.clear();
      _hostController.clear();
      _portController.clear();
      _usernameController.clear();
      _passwordController.clear();
      _deviceDetails = null; // Clear details when adding new device
      _showForm = true;
    });
  }

  Future<void> _deleteDevice(String id) async {
    try {
      await FirebaseFirestore.instance
          .collection('mikrotik_devices')
          .doc(id)
          .delete();
    } catch (e) {
      print('Error updating package: $e');
    }
    // final db = await SQLiteService().database;
    // await db.delete('mikrotik_devices', where: 'id = ?', whereArgs: [id]);
    Fluttertoast.showToast(msg: 'تم حذف جهاز المايكروتك بنجاح!');
    _loadAllMikrotikDevices();
    if (_selectedDevice?.id == id) {
      _clearForm();
      _showForm = false; // Hide form if the deleted device was selected
    }
  }

  Future<void> _testConnection() async {
    // التحقق من صحة النموذج أولاً
    if (!_formKey.currentState!.validate()) {
      Fluttertoast.showToast(msg: 'الرجاء ملء جميع الحقول المطلوبة بشكل صحيح.');
      return;
    }

    // إذا لم يكن هناك جهاز محدد، استخدم القيم من النموذج
    String host = _selectedDevice?.host ?? _hostController.text;
    int port = _selectedDevice?.port ?? int.parse(_portController.text);
    String username = _selectedDevice?.username ?? _usernameController.text;
    String password = _selectedDevice?.password ?? _passwordController.text;

    setState(() {
      _isLoading = true;
    });

    try {
      final isConnected = await _mikrotikService.testConnection(
        host,
        port,
        username,
        password,
      );

      if (isConnected) {
        Fluttertoast.showToast(msg: 'تم الاتصال بنجاح!');

        // إذا كان الاتصال ناجحاً وهذا جهاز جديد، يمكن جلب التفاصيل
        if (_selectedDevice == null) {
          // إنشاء جهاز مؤقت لجلب التفاصيل
          final currentUser = FirebaseAuth.instance.currentUser;
          if (currentUser == null) {
            Fluttertoast.showToast(msg: 'يجب تسجيل الدخول أولاً');
            return;
          }
          
          final tempDevice = MikrotikDeviceModel(
            adminId: currentUser.uid,
            name: _nameController.text,
            host: host,
            port: port,
            username: username,
            password: password,
          );
          await _fetchDeviceDetailsForTempDevice(tempDevice);
        }
      } else {
        Fluttertoast.showToast(msg: 'فشل الاتصال!');
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'خطأ أثناء اختبار الاتصال: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveMikrotikDevice() async {
    if (!_formKey.currentState!.validate()) return;

    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      Fluttertoast.showToast(msg: 'يجب تسجيل الدخول أولاً');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final newDevice = MikrotikDeviceModel(
        adminId: currentUser.uid, // استخدام معرف المستخدم الحالي
        id: _selectedDevice?.id,
        name: _nameController.text,
        host: _hostController.text,
        port: int.parse(_portController.text),
        username: _usernameController.text,
        password: _passwordController.text,
      );

      if (_selectedDevice == null) {
        // إنشاء جهاز جديد
        await FirebaseFirestore.instance
            .collection('mikrotik_devices')
            .doc(newDevice.id)
            .set(newDevice.toMap());
        
        Fluttertoast.showToast(msg: 'تمت إضافة جهاز المايكروتك بنجاح!');
      } else {
        // تحديث جهاز موجود
        await FirebaseFirestore.instance
            .collection('mikrotik_devices')
            .doc(newDevice.id)
            .update(newDevice.toMap());
            
        Fluttertoast.showToast(msg: 'تم تحديث جهاز المايكروتك بنجاح!');
      }
      
      // إعادة تحميل القائمة وتحديث الواجهة
      await _loadAllMikrotikDevices();
      setState(() {
        _showForm = false;
      });
    } catch (e) {
      print('Error saving MikroTik device: $e');
      Fluttertoast.showToast(msg: 'حدث خطأ أثناء حفظ الجهاز: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المايكروتك المتقدمة'),
        actions: [
          if (_selectedDevice != null && _showForm) ...[
            IconButton(
              icon: Icon(_isMonitoring ? Icons.pause : Icons.play_arrow),
              onPressed: _isMonitoring
                  ? _stopRealTimeMonitoring
                  : _startRealTimeMonitoring,
              tooltip: _isMonitoring ? 'إيقاف المراقبة' : 'بدء المراقبة',
            ),
            IconButton(
              icon: const Icon(Icons.restart_alt),
              onPressed: _rebootDevice,
              tooltip: 'إعادة تشغيل الجهاز',
            ),
          ],
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _showForm
          ? _buildAdvancedDeviceInterface()
          : _buildDeviceList(),
      floatingActionButton: _showForm
          ? null
          : FloatingActionButton(
              onPressed: _clearForm,
              child: const Icon(Icons.add),
            ),
    );
  }

  Widget _buildDeviceList() {
    return _allDevices.isEmpty
        ? const Center(
            child: Text(
              'لم يتم حفظ أي أجهزة مايكروتك. انقر على + لإضافة جهاز جديد.',
            ),
          )
        : ListView.builder(
            padding: const EdgeInsets.all(8.0),
            itemCount: _allDevices.length,
            itemBuilder: (context, index) {
              final device = _allDevices[index];
              return Card(
                margin: const EdgeInsets.symmetric(vertical: 8.0),
                child: ListTile(
                  title: Text(device.name),
                  subtitle: Text('${device.host}:${device.port}'),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.edit),
                        onPressed: () => _selectDevice(device),
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () => _deleteDevice(device.id!),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
  }

  Widget _buildDeviceFormContent() {
    return Column(
      children: [
        TextFormField(
          controller: _nameController,
          decoration: const InputDecoration(labelText: 'اسم الجهاز'),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'الرجاء إدخال اسم الجهاز';
            }
            return null;
          },
        ),
        TextFormField(
          controller: _hostController,
          decoration: const InputDecoration(labelText: 'المضيف (IP/النطاق)'),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'الرجاء إدخال المضيف';
            }
            return null;
          },
        ),
        TextFormField(
          controller: _portController,
          decoration: const InputDecoration(
            labelText: 'المنفذ (افتراضي: 8728)',
          ),
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'الرجاء إدخال المنفذ';
            }
            if (int.tryParse(value) == null) {
              return 'الرجاء إدخال رقم صالح';
            }
            return null;
          },
        ),
        TextFormField(
          controller: _usernameController,
          decoration: const InputDecoration(labelText: 'اسم المستخدم'),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'الرجاء إدخال اسم المستخدم';
            }
            return null;
          },
        ),
        TextFormField(
          controller: _passwordController,
          decoration: const InputDecoration(labelText: 'كلمة المرور'),
          obscureText: true,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'الرجاء إدخال كلمة المرور';
            }
            return null;
          },
        ),
        const SizedBox(height: 20),
        ElevatedButton(
          onPressed: _saveMikrotikDevice,
          child: Text(
            _selectedDevice == null
                ? 'إضافة جهاز مايكروتك'
                : 'تحديث جهاز مايكروتك',
          ),
        ),
        const SizedBox(height: 10),
        ElevatedButton(
          onPressed: _testConnection,
          child: const Text('اختبار الاتصال'),
        ),
        const SizedBox(height: 10),
        if (_selectedDevice != null)
          ElevatedButton.icon(
            onPressed: () => _fetchDeviceDetails(_selectedDevice!),
            icon: const Icon(Icons.refresh),
            label: const Text('تحديث التفاصيل'),
          ),
        const SizedBox(height: 10),
        TextButton(
          onPressed: () {
            setState(() {
              _showForm = false;
              _selectedDevice =
                  null; // Clear selected device when going back to list
              _deviceDetails =
                  null; // Clear device details when going back to list
            });
          },
          child: const Text('إلغاء'),
        ),
      ],
    );
  }

  Widget _buildAdvancedDeviceInterface() {
    // إذا لم يتم تحديد جهاز، عرض نموذج إضافة جهاز جديد
    if (_selectedDevice == null) {
      return SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'إضافة جهاز مايكروتك جديد',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 20),
              _buildDeviceFormContent(),
            ],
          ),
        ),
      );
    }

    return Column(
      children: [
        TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'نظرة عامة'),
            Tab(text: 'الواجهات'),
            Tab(text: 'المراقبة'),
            Tab(text: 'الأوامر'),
            Tab(text: 'إعدادات الجهاز'),
          ],
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(),
              _buildInterfacesTab(),
              _buildMonitoringTab(),
              _buildCommandTab(),
              _buildDeviceSettingsTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // عرض تفاصيل الجهاز فقط إذا كان هناك جهاز محدد وتم جلب التفاصيل
          if (_selectedDevice != null && _deviceDetails != null) ...[
            Card(
              margin: const EdgeInsets.all(0),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'تفاصيل الجهاز',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.refresh),
                          onPressed: () =>
                              _fetchDeviceDetails(_selectedDevice!),
                          tooltip: 'تحديث التفاصيل',
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    if (_deviceDetails!['status'] == 'error')
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          border: Border.all(color: Colors.red.shade200),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.error, color: Colors.red.shade600),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'خطأ: ${_deviceDetails!['message']}',
                                style: TextStyle(color: Colors.red.shade800),
                              ),
                            ),
                          ],
                        ),
                      )
                    else ...[
                      // System Information
                      _buildDetailSection('معلومات النظام', [
                        if (_deviceDetails!['identity'] != null)
                          _buildDetailRow(
                            'الهوية',
                            _deviceDetails!['identity'],
                          ),
                        if (_deviceDetails!['version'] != null)
                          _buildDetailRow(
                            'الإصدار',
                            _deviceDetails!['version'],
                          ),
                        if (_deviceDetails!['board_name'] != null)
                          _buildDetailRow(
                            'اللوحة',
                            _deviceDetails!['board_name'],
                          ),
                        if (_deviceDetails!['cpu'] != null)
                          _buildDetailRow('المعالج', _deviceDetails!['cpu']),
                        if (_deviceDetails!['cpu_count'] != null)
                          _buildDetailRow(
                            'عدد المعالجات',
                            _deviceDetails!['cpu_count'],
                          ),
                        if (_deviceDetails!['cpu_frequency'] != null)
                          _buildDetailRow(
                            'تردد المعالج',
                            _deviceDetails!['cpu_frequency'],
                          ),
                        if (_deviceDetails!['architecture_name'] != null)
                          _buildDetailRow(
                            'المعمارية',
                            _deviceDetails!['architecture_name'],
                          ),
                        if (_deviceDetails!['uptime'] != null)
                          _buildDetailRow(
                            'وقت التشغيل',
                            _deviceDetails!['uptime'],
                          ),
                        if (_deviceDetails!['cpu_load'] != null)
                          _buildDetailRow(
                            'استخدام المعالج',
                            _deviceDetails!['cpu_load'],
                          ),
                      ]),
                      // Memory Information
                      if (_deviceDetails!['total_memory'] != null ||
                          _deviceDetails!['free_memory'] != null) ...[
                        const SizedBox(height: 16),
                        _buildDetailSection('معلومات الذاكرة', [
                          if (_deviceDetails!['total_memory'] != null)
                            _buildDetailRow(
                              'إجمالي الذاكرة',
                              _deviceDetails!['total_memory'],
                            ),
                          if (_deviceDetails!['free_memory'] != null)
                            _buildDetailRow(
                              'الذاكرة الحرة',
                              _deviceDetails!['free_memory'],
                            ),
                        ]),
                      ],
                      // IP Addresses
                      if (_deviceDetails!['ip_addresses'] != null &&
                          (_deviceDetails!['ip_addresses'] as List)
                              .isNotEmpty) ...[
                        const SizedBox(height: 16),
                        _buildDetailSection('عناوين IP', [
                          ...((_deviceDetails!['ip_addresses'] as List)
                              .map(
                                (addr) => _buildDetailRow(
                                  addr['interface'] ?? 'غير معروف',
                                  '${addr['address']} (الشبكة: ${addr['network']})',
                                ),
                              )
                              .toList()),
                        ]),
                      ],
                      // Interfaces
                      if (_deviceDetails!['interfaces'] != null &&
                          (_deviceDetails!['interfaces'] as List)
                              .isNotEmpty) ...[
                        const SizedBox(height: 16),
                        _buildDetailSection('واجهات الشبكة', [
                          ...((_deviceDetails!['interfaces'] as List)
                              .map(
                                (interface) => _buildDetailRow(
                                  interface['name'] ?? 'غير معروف',
                                  '${interface['type']} - ${interface['running'] == 'true' ? 'قيد التشغيل' : 'غير قيد التشغيل'} - MTU: ${interface['mtu']}',
                                ),
                              )
                              .toList()),
                        ]),
                      ],
                      // Wireless Interfaces
                      if (_deviceDetails!['wireless_interfaces'] != null &&
                          (_deviceDetails!['wireless_interfaces'] as List)
                              .isNotEmpty) ...[
                        const SizedBox(height: 16),
                        _buildDetailSection('واجهات الشبكة اللاسلكية', [
                          ...((_deviceDetails!['wireless_interfaces'] as List)
                              .map(
                                (wifi) => _buildDetailRow(
                                  wifi['name'] ?? 'غير معروف',
                                  'SSID: ${wifi['ssid']} - الوضع: ${wifi['mode']} - التردد: ${wifi['frequency']}',
                                ),
                              )
                              .toList()),
                        ]),
                      ],
                      // DHCP Servers
                      if (_deviceDetails!['dhcp_servers'] != null &&
                          (_deviceDetails!['dhcp_servers'] as List)
                              .isNotEmpty) ...[
                        const SizedBox(height: 16),
                        _buildDetailSection('خوادم DHCP', [
                          ...((_deviceDetails!['dhcp_servers'] as List)
                              .map(
                                (dhcp) => _buildDetailRow(
                                  dhcp['name'] ?? 'غير معروف',
                                  'الواجهة: ${dhcp['interface']} - المجمع: ${dhcp['address_pool']}',
                                ),
                              )
                              .toList()),
                        ]),
                      ],
                      const SizedBox(height: 12),
                      if (_deviceDetails!['last_updated'] != null)
                        Text(
                          'آخر تحديث: ${DateTime.parse(_deviceDetails!['last_updated']).toLocal().toString().substring(0, 19)}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInterfacesTab() {
    if (_selectedDevice == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.info_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'يجب حفظ الجهاز أولاً لعرض تفاصيل الواجهات',
              style: TextStyle(fontSize: 16, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              'قم بملء النموذج واضغط على "إضافة جهاز مايكروتك"',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_deviceDetails == null || _deviceDetails!['status'] == 'error') {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('فشل في جلب تفاصيل الواجهات.'),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: () => _fetchDeviceDetails(_selectedDevice!),
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    List<dynamic> interfaces = _deviceDetails!['interfaces'] ?? [];
    Map<String, dynamic> trafficData = _deviceDetails!['traffic_data'] ?? {};

    return RefreshIndicator(
      onRefresh: () => _fetchDeviceDetails(_selectedDevice!),
      child: ListView.builder(
        padding: const EdgeInsets.all(16.0),
        itemCount: interfaces.length,
        itemBuilder: (context, index) {
          var interface = interfaces[index];
          String name = interface['name'] ?? 'غير معروف';
          bool isRunning = interface['running'] == 'true';
          bool isDisabled = interface['disabled'] == 'true';
          String type = interface['type'] ?? 'Unknown';
          String mtu = interface['mtu'] ?? 'Unknown';

          var traffic = trafficData[name] ?? {};
          int rxBytes = traffic['rx_bytes'] ?? 0;
          int txBytes = traffic['tx_bytes'] ?? 0;

          return Card(
            margin: const EdgeInsets.symmetric(vertical: 8.0),
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildDetailRow('النوع', type),
                  _buildDetailRow(
                    'الحالة',
                    isRunning ? 'قيد التشغيل' : 'متوقف',
                  ),
                  _buildDetailRow('معطل', isDisabled ? 'نعم' : 'لا'),
                  _buildDetailRow('MTU', mtu),
                  const SizedBox(height: 8),
                  _buildDetailRow('استقبال (بايت)', _formatBytes(rxBytes)),
                  _buildDetailRow('إرسال (بايت)', _formatBytes(txBytes)),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      ElevatedButton.icon(
                        onPressed: () async {
                          String result = await _mikrotikService
                              .setInterfaceStatus(
                                _selectedDevice!,
                                name,
                                !isRunning, // Toggle status
                              );
                          Fluttertoast.showToast(msg: result);
                          _fetchDeviceDetails(
                            _selectedDevice!,
                          ); // Refresh after action
                        },
                        icon: Icon(isRunning ? Icons.stop : Icons.play_arrow),
                        label: Text(isRunning ? 'تعطيل' : 'تفعيل'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: isRunning
                              ? Colors.orange
                              : Colors.green,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  String _formatBytes(int bytes) {
    if (bytes <= 0) return '0 B';
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    int i = (math.log(bytes) / math.log(1024)).floor();
    return '${(bytes / math.pow(1024, i)).toStringAsFixed(2)} ${suffixes[i]}';
  }

  Widget _buildMonitoringTab() {
    if (_selectedDevice == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.monitor, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'يجب حفظ الجهاز أولاً لبدء المراقبة',
              style: TextStyle(fontSize: 16, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              'قم بملء النموذج واضغط على "إضافة جهاز مايكروتك"',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (!_isMonitoring) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('المراقبة متوقفة. اضغط على زر التشغيل للبدء.'),
            const SizedBox(height: 10),
            ElevatedButton.icon(
              onPressed: _startRealTimeMonitoring,
              icon: const Icon(Icons.play_arrow),
              label: const Text('بدء المراقبة'),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // System Resources Section with header
          const Padding(
            padding: EdgeInsets.only(bottom: 10.0, top: 6.0),
            child: Text(
              'موارد النظام',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
          _buildChartCard(
            'استخدام المعالج (%)',
            _cpuUsageData,
            Colors.blue,
            minY: 0,
            maxY: 100,
          ),
          const SizedBox(height: 20),
          _buildChartCard(
            'استخدام الذاكرة (%)',
            _memoryUsageData,
            Colors.green,
            minY: 0,
            maxY: 100,
          ),

          // Interface Traffic Charts with improved header
          if (_interfaceTrafficData.isNotEmpty) ...[
            const SizedBox(height: 30),
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1),
                ),
              ),
              child: const Text(
                'مراقبة حركة مرور الواجهات',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(height: 16),
            // Display the new combined traffic charts
            ..._interfaceTrafficData.keys.where((key) => key.endsWith('_rx')).map((
              key,
            ) {
              String interfaceName = key.replaceAll('_rx', '');

              // Verificar si existen las listas y asegurar que no son nulas
              final rxList = _interfaceTrafficData[key] ?? [];
              final txList = _interfaceTrafficData['${interfaceName}_tx'] ?? [];

              // Si no hay suficientes datos para calcular Mbps, no mostrar esta interfaz aún
              if (rxList.length < 2 && txList.length < 2) {
                return const SizedBox.shrink(); // No mostrar nada hasta tener datos suficientes
              }

              // Convert raw byte data to Mbps con seguridad adicional
              List<FlSpot> rxData = _convertToMbps(rxList);
              List<FlSpot> txData = _convertToMbps(txList);

              // Calculate appropriate Y axis scale con comprobación segura
              double maxTraffic =
                  0.1; // Valor mínimo para evitar errores de escala

              // Usar reduce de manera segura solo si hay elementos
              try {
                if (rxData.isNotEmpty) {
                  double maxRx = rxData
                      .map((e) => e.y)
                      .reduce((a, b) => math.max(a, b));
                  if (maxRx > 0) maxTraffic = math.max(maxTraffic, maxRx);
                }
                if (txData.isNotEmpty) {
                  double maxTx = txData
                      .map((e) => e.y)
                      .reduce((a, b) => math.max(a, b));
                  if (maxTx > 0) maxTraffic = math.max(maxTraffic, maxTx);
                }
              } catch (e) {
                // Si hay algún error en el cálculo, usar un valor predeterminado
                maxTraffic = 1.0;
              }

              // Add buffer to max traffic for better visualization
              maxTraffic = (maxTraffic * 1.2).ceilToDouble();
              // Set minimum of 1 Mbps if traffic is very low, for better visualization
              maxTraffic = math.max(maxTraffic, 1.0);

              return Padding(
                padding: const EdgeInsets.only(bottom: 20.0),
                child: _buildCombinedTrafficChartCard(
                  'واجهة $interfaceName - حركة المرور',
                  rxData,
                  txData,
                  Colors.blue.shade700, // RX color
                  Colors.orange.shade700, // TX color
                  maxY: maxTraffic,
                ),
              );
            }),
          ],
        ],
      ),
    );
  }

  Widget _buildChartCard(
    String title,
    List<FlSpot> data,
    Color color, {
    double minY = 0,
    double maxY = 100,
  }) {
    // تأكد من وجود نقطتين على الأقل في البيانات
    if (data.isEmpty) {
      data = [FlSpot(0, 0), FlSpot(1, 0)];
    } else if (data.length == 1) {
      data = [data.first, FlSpot(data.first.x + 1, data.first.y)];
    }
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: false),
                  titlesData: const FlTitlesData(show: false),
                  borderData: FlBorderData(
                    show: true,
                    border: Border.all(
                      color: const Color(0xff37434d),
                      width: 1,
                    ),
                  ),
                  minX: data.isNotEmpty ? data.first.x : 0,
                  maxX: data.isNotEmpty ? data.last.x : 20,
                  minY: minY,
                  maxY: maxY,
                  lineBarsData: [
                    LineChartBarData(
                      spots: data,
                      isCurved: true,
                      color: color,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: const FlDotData(show: false),
                      belowBarData: BarAreaData(show: false),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommandTab() {
    // إذا لم يتم حفظ الجهاز بعد، عرض رسالة توضيحية
    if (_selectedDevice == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.info_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            const Text(
              'يجب حفظ الجهاز أولاً',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'لاستخدام ميزة الأوامر المخصصة، يجب حفظ الجهاز في قاعدة البيانات أولاً',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          TextField(
            controller: _commandController,
            decoration: InputDecoration(
              labelText: 'أمر مخصص للمايكروتك',
              suffixIcon: IconButton(
                icon: const Icon(Icons.send),
                onPressed: () => _executeCustomCommand(_commandController.text),
              ),
            ),
            onSubmitted: _executeCustomCommand,
          ),
          const SizedBox(height: 10),
          Row(
            children: [
              Switch(
                value: _useSSHForCommand,
                onChanged: (val) {
                  setState(() {
                    _useSSHForCommand = val;
                  });
                },
              ),
              const Text('تنفيذ عبر SSH (CLI)'),
            ],
          ),
          ElevatedButton(
            onPressed: () => _executeCustomCommand(_commandController.text),
            child: const Text('تنفيذ الأمر'),
          ),
          const SizedBox(height: 10),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'النتائج:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 5),
                  Container(
                    padding: const EdgeInsets.all(8.0),
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(5),
                    ),
                    child: Text(_commandResult),
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'سجل الأوامر:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 5),
                  ..._commandHistory.map(
                    (cmd) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2.0),
                      child: Text(
                        cmd,
                        style: const TextStyle(fontFamily: 'monospace'),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailSection(String title, List<Widget> children) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  // Converts a list of FlSpot (where y is bytes) to Mbps (List<FlSpot>), assuming 5 seconds between points
  List<FlSpot> _convertToMbps(List<FlSpot> bytesData) {
    List<FlSpot> mbpsData = [];
    if (bytesData.isEmpty) {
      return [FlSpot(0, 0)];
    } else if (bytesData.length < 2) {
      return [FlSpot(0, 0)];
    }
    for (int i = 1; i < bytesData.length; i++) {
      double bytesDelta = bytesData[i].y - bytesData[i - 1].y;
      if (bytesDelta < 0) {
        bytesDelta = bytesData[i].y;
      }
      double mbps = (bytesDelta * 8) / (1000 * 1000) / 5;
      mbps = math.max(mbps.abs(), 0.01);
      mbpsData.add(FlSpot(bytesData[i].x, mbps));
    }
    if (mbpsData.isEmpty) {
      mbpsData.add(FlSpot(0, 0));
    }
    return mbpsData;
  }

  Widget _buildCombinedTrafficChartCard(
    String title,
    List<FlSpot> rxData,
    List<FlSpot> txData,
    Color rxColor,
    Color txColor, {
    double minY = 0,
    required double maxY,
  }) {
    // تأكد من وجود نقطتين على الأقل في كل قائمة بيانات
    if (rxData.isEmpty) {
      rxData = [FlSpot(0, 0), FlSpot(1, 0)];
    } else if (rxData.length == 1) {
      rxData = [rxData.first, FlSpot(rxData.first.x + 1, rxData.first.y)];
    }
    if (txData.isEmpty) {
      txData = [FlSpot(0, 0), FlSpot(1, 0)];
    } else if (txData.length == 1) {
      txData = [txData.first, FlSpot(txData.first.x + 1, txData.first.y)];
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            // Legend with better styling
            Row(
              children: [
                Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: rxColor,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 4),
                    const Text(
                      'استقبال (RX)',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
                const SizedBox(width: 24),
                Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: txColor,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 4),
                    const Text(
                      'إرسال (TX)',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 250,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: true,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: const Color(0xffe7e8ec),
                        strokeWidth: 1,
                      );
                    },
                    getDrawingVerticalLine: (value) {
                      return FlLine(
                        color: const Color(0xffe7e8ec),
                        strokeWidth: 1,
                      );
                    },
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    bottomTitles: AxisTitles(
                      axisNameWidget: const Padding(
                        padding: EdgeInsets.only(top: 8.0),
                        child: Text(
                          'الوقت',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF6E7681),
                          ),
                        ),
                      ),
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        getTitlesWidget: (value, meta) {
                          // Show only some time ticks for clarity
                          if (value % 5 == 0) {
                            return Padding(
                              padding: const EdgeInsets.only(top: 5.0),
                              child: Text(
                                value.toInt().toString(),
                                style: const TextStyle(
                                  fontSize: 10,
                                  color: Color(0xFF6E7681),
                                ),
                              ),
                            );
                          }
                          return const SizedBox();
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      axisNameWidget: const Padding(
                        padding: EdgeInsets.only(bottom: 8.0),
                        child: Text(
                          'ميجابت/ثانية',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF6E7681),
                          ),
                        ),
                      ),
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 45,
                        getTitlesWidget: (value, meta) {
                          return Text(
                            value.toStringAsFixed(1),
                            style: const TextStyle(
                              fontSize: 10,
                              color: Color(0xFF6E7681),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border.all(
                      color: const Color(0xff37434d),
                      width: 1,
                    ),
                  ), // Configurar límites X con un valor mínimo de diferencia para evitar errores de fl_chart
                  minX: rxData.isNotEmpty ? rxData.first.x : 0,
                  maxX: rxData.isNotEmpty
                      ? math.max(rxData.first.x + 1, rxData.last.x)
                      : 20,
                  minY: minY,
                  maxY: math.max(
                    maxY,
                    0.1,
                  ), // Asegurar un valor mínimo de Y para evitar errores
                  lineBarsData: [
                    // RX Data
                    LineChartBarData(
                      spots: rxData,
                      isCurved: true,
                      curveSmoothness: 0.3,
                      color: rxColor,
                      barWidth: 2.5,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 2.5,
                            color: rxColor,
                            strokeWidth: 1,
                            strokeColor: Colors.white,
                          );
                        },
                        checkToShowDot: (spot, barData) {
                          // Only show dots at intervals to avoid clutter
                          return spot.x.toInt() % 5 == 0;
                        },
                      ),
                      belowBarData: BarAreaData(
                        show: true,
                        color: rxColor.withOpacity(0.15),
                        applyCutOffY: true,
                        cutOffY: 0,
                        spotsLine: const BarAreaSpotsLine(show: false),
                      ),
                    ),
                    // TX Data
                    LineChartBarData(
                      spots: txData,
                      isCurved: true,
                      curveSmoothness: 0.3,
                      color: txColor,
                      barWidth: 2.5,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 2.5,
                            color: txColor,
                            strokeWidth: 1,
                            strokeColor: Colors.white,
                          );
                        },
                        checkToShowDot: (spot, barData) {
                          // Only show dots at intervals to avoid clutter
                          return spot.x.toInt() % 5 == 0;
                        },
                      ),
                      belowBarData: BarAreaData(
                        show: true,
                        color: txColor.withOpacity(0.15),
                        applyCutOffY: true,
                        cutOffY: 0,
                        spotsLine: const BarAreaSpotsLine(show: false),
                      ),
                    ),
                  ],
                ),
              ),
            ), // Additional stats below chart
            if (rxData.length > 1 && txData.length > 1) ...[
              const SizedBox(height: 12),
              Wrap(
                spacing: 16,
                runSpacing: 8,
                children: [
                  _buildStatItem(
                    'أقصى استقبال',
                    '${_safeReduce(rxData.map((e) => e.y), (a, b) => a > b ? a : b, 0).toStringAsFixed(2)} Mbps',
                    rxColor,
                  ),
                  _buildStatItem(
                    'أقصى إرسال',
                    '${_safeReduce(txData.map((e) => e.y), (a, b) => a > b ? a : b, 0).toStringAsFixed(2)} Mbps',
                    txColor,
                  ),
                  _buildStatItem(
                    'متوسط استقبال',
                    '${(rxData.isEmpty ? 0 : _safeReduce(rxData.map((e) => e.y), (a, b) => a + b, 0) / rxData.length).toStringAsFixed(2)} Mbps',
                    rxColor,
                  ),
                  _buildStatItem(
                    'متوسط إرسال',
                    '${(txData.isEmpty ? 0 : _safeReduce(txData.map((e) => e.y), (a, b) => a + b, 0) / txData.length).toStringAsFixed(2)} Mbps',
                    txColor,
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: color.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: const TextStyle(fontSize: 13, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  // Función auxiliar para realizar una reducción segura con un valor predeterminado
  double _safeReduce(
    Iterable<double> values,
    double Function(double a, double b) combine,
    double defaultValue,
  ) {
    if (values.isEmpty) return defaultValue;
    try {
      return values.reduce(combine);
    } catch (e) {
      return defaultValue;
    }
  }

  // دالة مساعدة لجلب تفاصيل جهاز مؤقت (للأجهزة الجديدة قبل الحفظ)
  Future<void> _fetchDeviceDetailsForTempDevice(
    MikrotikDeviceModel tempDevice,
  ) async {
    if (_disposed) return;
    if (mounted) {
      setState(() {
        _isLoading = true;
        _deviceDetails = null;
      });
    }
    try {
      final details = await _mikrotikService.getMikrotikDeviceDetails(
        tempDevice,
      );
      final trafficDetails = await _mikrotikService.getInterfaceTraffic(
        tempDevice,
      );

      if (!_disposed && mounted) {
        setState(() {
          _deviceDetails = {
            ...details,
            'traffic_data': trafficDetails['traffic_data'] ?? {},
            'last_updated': DateTime.now().toIso8601String(),
          };
        });
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'خطأ أثناء جلب تفاصيل الجهاز: $e');
      if (!_disposed && mounted) {
        setState(() {
          _deviceDetails = {
            'error': 'Failed to load details',
            'message': e.toString(),
          };
        });
      }
    } finally {
      if (!_disposed && mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // تبويب إعدادات الجهاز
  Widget _buildDeviceSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(key: _formKey, child: _buildDeviceFormContent()),
    );
  }
}
