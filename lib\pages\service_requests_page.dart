import 'package:flutter/material.dart';
import 'dart:math';
import '../models/service_request_model.dart';
import '../models/tower_model.dart';
import '../services/tower_service.dart';
import '../services/database_service.dart';
import '../services/app_settings_service.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:url_launcher/url_launcher.dart';

class ServiceRequestsPage extends StatefulWidget {
  const ServiceRequestsPage({Key? key}) : super(key: key);

  @override
  State<ServiceRequestsPage> createState() => _ServiceRequestsPageState();
}

class _ServiceRequestsPageState extends State<ServiceRequestsPage> {
  final TowerService _towerService = TowerService();
  final DatabaseService _databaseService = DatabaseService();
  
  List<ServiceRequestModel> _requests = [];
  bool _isLoading = true;
  String? _adminId;
  String _filterStatus = 'all';
  String _currencySymbol = 'د.ع'; // رمز العملة الافتراضي
  TowerModel? _selectedTower; // البرج المحدد للعرض على الخريطة

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      final user = await _databaseService.getCurrentUser();
      if (user != null) {
        _adminId = user.adminId;
        await _loadRequests();
      }
      
      // تحميل إعدادات العملة
      final currencySymbol = await AppSettingsService.getCurrencySymbol();
      setState(() {
        _currencySymbol = currencySymbol;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تحميل البيانات: $e')),
      );
    }
    
    setState(() => _isLoading = false);
  }

  Future<void> _loadRequests() async {
    if (_adminId == null) return;
    
    final requests = await _towerService.getServiceRequestsByAdmin(_adminId!);
    setState(() {
      _requests = requests;
    });
  }

  List<ServiceRequestModel> get _filteredRequests {
    if (_filterStatus == 'all') {
      return _requests;
    }
    return _requests.where((request) => request.status.name == _filterStatus).toList();
  }

  Future<void> _updateRequestStatus(ServiceRequestModel request, RequestStatus newStatus) async {
    try {
      await _towerService.updateServiceRequestStatus(request.id, newStatus, null);
      await _loadRequests();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('تم تحديث حالة الطلب إلى ${newStatus.name}')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تحديث حالة الطلب: $e')),
      );
    }
  }

  void _showRequestDetails(ServiceRequestModel request) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.85,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        expand: false,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: request.statusColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.person,
                        color: request.statusColor,
                        size: 28,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            request.subscriberName,
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            request.subscriberPhone,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: request.statusColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: request.statusColor.withOpacity(0.3)),
                      ),
                      child: Text(
                        request.statusText,
                        style: TextStyle(
                          color: request.statusColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Content
              Expanded(
                child: FutureBuilder<TowerModel?>(
                  future: _getTowerInfo(request.towerId),
                  builder: (context, snapshot) {
                    final tower = snapshot.data;
                    final distance = tower != null ? _calculateDistance(request, tower) : null;
                    
                    return SingleChildScrollView(
                      controller: scrollController,
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Service Details Card
                          Card(
                            elevation: 2,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(Icons.info_outline, color: Colors.blue.shade600, size: 20),
                                      const SizedBox(width: 8),
                                      Text(
                                        'تفاصيل الخدمة',
                                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  _buildDetailRow('البرج', request.towerName, Icons.cell_tower),
                                  _buildDetailRow('الباقة المختارة', request.selectedPackage, Icons.inventory),
                                  _buildDetailRow('السعر', '${request.packagePrice} $_currencySymbol', Icons.attach_money),
                                  if (distance != null)
                                    _buildDetailRow('المسافة', '${distance.toStringAsFixed(1)} كم', Icons.straighten),
                                  _buildDetailRow('تاريخ الطلب', _formatDate(request.createdAt), Icons.schedule),
                                  if (request.updatedAt != null)
                                    _buildDetailRow('آخر تحديث', _formatDate(request.updatedAt!), Icons.update),
                                  if (request.notes != null && request.notes!.isNotEmpty)
                                    _buildDetailRow('ملاحظات', request.notes!, Icons.note),
                                ],
                              ),
                            ),
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // Map Card (Smaller)
                          if (tower != null) ...[
                            Card(
                              elevation: 2,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.all(16),
                                    child: Row(
                                      children: [
                                        Icon(Icons.location_on, color: Colors.red.shade600, size: 20),
                                        const SizedBox(width: 8),
                                        Text(
                                          'الموقع والمسافة',
                                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  
                                  // Smaller Map
                                  Container(
                                    height: 150,
                                    margin: const EdgeInsets.symmetric(horizontal: 16),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(color: Colors.grey.shade300),
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: FlutterMap(
                                        options: MapOptions(
                                          initialCenter: LatLng(
                                            (request.subscriberLatitude + tower.latitude) / 2,
                                            (request.subscriberLongitude + tower.longitude) / 2,
                                          ),
                                          initialZoom: 13,
                                          interactionOptions: const InteractionOptions(
                                            enableMultiFingerGestureRace: false,
                                          ),
                                        ),
                                        children: [
                                          TileLayer(
                                            urlTemplate: 'https://cartodb-basemaps-{s}.global.ssl.fastly.net/light_all/{z}/{x}/{y}.png',
                                            subdomains: const ['a', 'b', 'c', 'd'],
                                            userAgentPackageName: 'com.example.isp_manager',
                                          ),
                                          
                                          CircleLayer(
                                            circles: [
                                              CircleMarker(
                                                point: LatLng(tower.latitude, tower.longitude),
                                                radius: tower.coverageRadius * 1000,
                                                color: Colors.blue.withOpacity(0.2),
                                                borderColor: Colors.blue,
                                                borderStrokeWidth: 1,
                                              ),
                                            ],
                                          ),
                                          
                                          PolylineLayer(
                                            polylines: [
                                              Polyline(
                                                points: [
                                                  LatLng(request.subscriberLatitude, request.subscriberLongitude),
                                                  LatLng(tower.latitude, tower.longitude),
                                                ],
                                                strokeWidth: 2,
                                                color: Colors.red,
                                              ),
                                            ],
                                          ),
                                          
                                          MarkerLayer(
                                            markers: [
                                              Marker(
                                                point: LatLng(request.subscriberLatitude, request.subscriberLongitude),
                                                width: 20,
                                                height: 20,
                                                child: Container(
                                                  decoration: BoxDecoration(
                                                    color: Colors.red,
                                                    shape: BoxShape.circle,
                                                    border: Border.all(color: Colors.white, width: 1),
                                                  ),
                                                  child: const Icon(
                                                    Icons.person,
                                                    color: Colors.white,
                                                    size: 12,
                                                  ),
                                                ),
                                              ),
                                              Marker(
                                                point: LatLng(tower.latitude, tower.longitude),
                                                width: 20,
                                                height: 20,
                                                child: Container(
                                                  decoration: BoxDecoration(
                                                    color: Colors.blue,
                                                    shape: BoxShape.circle,
                                                    border: Border.all(color: Colors.white, width: 1),
                                                  ),
                                                  child: const Icon(
                                                    Icons.cell_tower,
                                                    color: Colors.white,
                                                    size: 12,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  
                                  const SizedBox(height: 16),
                                  
                                  // Distance, Direction and Navigation
                                  Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: 16),
                                    child: Column(
                                      children: [
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Container(
                                                padding: const EdgeInsets.all(12),
                                                decoration: BoxDecoration(
                                                  color: Colors.blue.shade50,
                                                  borderRadius: BorderRadius.circular(8),
                                                  border: Border.all(color: Colors.blue.shade200),
                                                ),
                                                child: Column(
                                                  children: [
                                                    Icon(Icons.straighten, color: Colors.blue.shade600, size: 20),
                                                    const SizedBox(height: 4),
                                                    Text(
                                                      'المسافة',
                                                      style: TextStyle(
                                                        fontSize: 12,
                                                        color: Colors.grey.shade600,
                                                      ),
                                                    ),
                                                    Text(
                                                      '${distance?.toStringAsFixed(1) ?? 'غير محدد'} كم',
                                                      style: TextStyle(
                                                        fontWeight: FontWeight.bold,
                                                        color: Colors.blue.shade700,
                                                        fontSize: 14,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            const SizedBox(width: 12),
                                            Expanded(
                                              child: Container(
                                                padding: const EdgeInsets.all(12),
                                                decoration: BoxDecoration(
                                                  color: Colors.green.shade50,
                                                  borderRadius: BorderRadius.circular(8),
                                                  border: Border.all(color: Colors.green.shade200),
                                                ),
                                                child: Column(
                                                  children: [
                                                    Container(
                                                      width: 40,
                                                      height: 40,
                                                      decoration: BoxDecoration(
                                                        color: Colors.green.shade100,
                                                        shape: BoxShape.circle,
                                                        border: Border.all(color: Colors.green.shade300, width: 2),
                                                      ),
                                                      child: Transform.rotate(
                                                        angle: (_calculateBearing(request, tower) * pi / 180),
                                                        child: Icon(Icons.navigation, color: Colors.green.shade600, size: 20),
                                                      ),
                                                    ),
                                                    const SizedBox(height: 4),
                                                    Text(
                                                      'الاتجاه',
                                                      style: TextStyle(
                                                        fontSize: 12,
                                                        color: Colors.grey.shade600,
                                                      ),
                                                    ),
                                                    Text(
                                                      '${_calculateBearing(request, tower).toStringAsFixed(0)}°',
                                                      style: TextStyle(
                                                        fontWeight: FontWeight.bold,
                                                        color: Colors.green.shade700,
                                                        fontSize: 14,
                                                      ),
                                                    ),
                                                    Text(
                                                      _getDirectionText(_calculateBearing(request, tower)),
                                                      style: TextStyle(
                                                        fontSize: 10,
                                                        color: Colors.green.shade600,
                                                        fontWeight: FontWeight.w500,
                                                      ),
                                                    ),
                                                    Text(
                                                      'من موقع المشترك',
                                                      style: TextStyle(
                                                        fontSize: 8,
                                                        color: Colors.grey.shade500,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 12),
                                        SizedBox(
                                          width: double.infinity,
                                          child: ElevatedButton.icon(
                                            onPressed: () => _openMapsWithDirections(request, tower),
                                            icon: const Icon(Icons.directions, size: 18),
                                            label: const Text('فتح التوجيه في الخرائط', style: TextStyle(fontSize: 12)),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.green,
                                              foregroundColor: Colors.white,
                                              padding: const EdgeInsets.symmetric(vertical: 12),
                                              shape: RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(8),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  
                                  const SizedBox(height: 16),
                                ],
                              ),
                            ),
                          ],
                          
                          const SizedBox(height: 20),
                          
                          // Action Buttons
                          ...(request.status == RequestStatus.pending ? [
                            Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton.icon(
                                    icon: const Icon(Icons.check, size: 18),
                                    label: const Text('موافقة'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(vertical: 16),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    onPressed: () {
                                      Navigator.of(context).pop();
                                      _updateRequestStatus(request, RequestStatus.approved);
                                    },
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    icon: const Icon(Icons.close, size: 18),
                                    label: const Text('رفض'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.red,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(vertical: 16),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    onPressed: () {
                                      Navigator.of(context).pop();
                                      _updateRequestStatus(request, RequestStatus.rejected);
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ] : []),
                          ...(request.status == RequestStatus.approved ? [
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                icon: const Icon(Icons.done_all, size: 18),
                                label: const Text('إكمال الخدمة'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                onPressed: () {
                                  Navigator.of(context).pop();
                                  _updateRequestStatus(request, RequestStatus.completed);
                                },
                              ),
                            ),
                          ] : []),
                          
                          const SizedBox(height: 20),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData? icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (icon != null) ...[
                  Icon(icon, color: Colors.grey.shade600, size: 18),
                  const SizedBox(width: 8),
                ],
                Expanded(child: Text(value)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  // حساب المسافة بين المشترك والبرج
  double _calculateDistance(ServiceRequestModel request, TowerModel tower) {
    return tower.distanceTo(request.subscriberLatitude, request.subscriberLongitude);
  }

  // حساب الاتجاه (bearing) من المشترك إلى البرج بالدرجات
  double _calculateBearing(ServiceRequestModel request, TowerModel tower) {
    const double earthRadius = 6371; // نصف قطر الأرض بالكيلومترات
    
    double lat1 = request.subscriberLatitude * (pi / 180);
    double lat2 = tower.latitude * (pi / 180);
    double deltaLng = (tower.longitude - request.subscriberLongitude) * (pi / 180);
    
    double y = sin(deltaLng) * cos(lat2);
    double x = cos(lat1) * sin(lat2) - sin(lat1) * cos(lat2) * cos(deltaLng);
    
    double bearing = atan2(y, x) * (180 / pi);
    
    // تحويل إلى 0-360 درجة
    bearing = (bearing + 360) % 360;
    
    return bearing;
  }

  // تحويل الدرجات إلى اتجاه نصي
  String _getDirectionText(double bearing) {
    if (bearing >= 337.5 || bearing < 22.5) return 'شمال';
    if (bearing >= 22.5 && bearing < 67.5) return 'شمال شرق';
    if (bearing >= 67.5 && bearing < 112.5) return 'شرق';
    if (bearing >= 112.5 && bearing < 157.5) return 'جنوب شرق';
    if (bearing >= 157.5 && bearing < 202.5) return 'جنوب';
    if (bearing >= 202.5 && bearing < 247.5) return 'جنوب غرب';
    if (bearing >= 247.5 && bearing < 292.5) return 'غرب';
    if (bearing >= 292.5 && bearing < 337.5) return 'شمال غرب';
    return 'شمال';
  }

  // فتح الخرائط مع التوجيه
  Future<void> _openMapsWithDirections(ServiceRequestModel request, TowerModel tower) async {
    final subscriberLat = request.subscriberLatitude;
    final subscriberLng = request.subscriberLongitude;
    final towerLat = tower.latitude;
    final towerLng = tower.longitude;

    // رابط Google Maps مع التوجيه
    final url = 'https://www.google.com/maps/dir/$subscriberLat,$subscriberLng/$towerLat,$towerLng';
    
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('لا يمكن فتح الخرائط')),
      );
    }
  }

  // جلب معلومات البرج
  Future<TowerModel?> _getTowerInfo(String towerId) async {
    if (_selectedTower?.id == towerId) return _selectedTower;
    
    try {
      final tower = await _towerService.getTowerById(towerId);
      if (tower != null) {
        setState(() {
          _selectedTower = tower;
        });
      }
      return tower;
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('طلبات الخدمة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadRequests,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // فلتر الحالة
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      const Text('فلتر الحالة: '),
                      const SizedBox(width: 8),
                      DropdownButton<String>(
                        value: _filterStatus,
                        items: [
                          const DropdownMenuItem(value: 'all', child: Text('الكل')),
                          ...RequestStatus.values.map((status) {
                            String statusText;
                            switch (status) {
                              case RequestStatus.pending:
                                statusText = 'في الانتظار';
                                break;
                              case RequestStatus.approved:
                                statusText = 'تمت الموافقة';
                                break;
                              case RequestStatus.rejected:
                                statusText = 'مرفوض';
                                break;
                              case RequestStatus.completed:
                                statusText = 'مكتمل';
                                break;
                              case RequestStatus.cancelled:
                                statusText = 'ملغي';
                                break;
                            }
                            return DropdownMenuItem<String>(
                              value: status.name,
                              child: Text(statusText),
                            );
                          }).toList(),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _filterStatus = value ?? 'all';
                          });
                        },
                      ),
                    ],
                  ),
                ),
                // قائمة الطلبات
                Expanded(
                  child: _filteredRequests.isEmpty
                      ? const Center(
                          child: Text('لا توجد طلبات خدمة'),
                        )
                      : ListView.builder(
                          itemCount: _filteredRequests.length,
                          itemBuilder: (context, index) {
                            final request = _filteredRequests[index];
                            return Card(
                              margin: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 4,
                              ),
                              child: ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: request.statusColor.withOpacity(0.1),
                                  child: Icon(
                                    Icons.person,
                                    color: request.statusColor,
                                  ),
                                ),
                                title: Text(request.subscriberName),
                                subtitle: FutureBuilder<TowerModel?>(
                                  future: _getTowerInfo(request.towerId),
                                  builder: (context, snapshot) {
                                    final tower = snapshot.data;
                                    final distance = tower != null ? _calculateDistance(request, tower) : null;
                                    final bearing = tower != null ? _calculateBearing(request, tower) : null;
                                    
                                    return Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(request.subscriberPhone),
                                        Text('البرج: ${request.towerName}'),
                                        Text('الباقة: ${request.selectedPackage}'),
                                        Text('السعر: ${request.packagePrice} $_currencySymbol'),
                                        if (distance != null) Text('المسافة: ${distance.toStringAsFixed(1)} كم'),
                                        if (bearing != null) Row(
                                          children: [
                                            Transform.rotate(
                                              angle: (bearing * pi / 180),
                                              child: Icon(Icons.navigation, color: Colors.green.shade600, size: 14),
                                            ),
                                            const SizedBox(width: 4),
                                            Text('${bearing.toStringAsFixed(0)}° ${_getDirectionText(bearing)}'),
                                          ],
                                        ),
                                        Text('التاريخ: ${_formatDate(request.createdAt)}'),
                                      ],
                                    );
                                  },
                                ),
                                trailing: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: request.statusColor.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    request.statusText,
                                    style: TextStyle(
                                      color: request.statusColor,
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                onTap: () => _showRequestDetails(request),
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
    );
  }
} 