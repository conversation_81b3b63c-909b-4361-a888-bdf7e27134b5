import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'dart:io';
import '../services/firebase_subscription_service.dart';
import 'package:local_auth/local_auth.dart'; // Import for local_auth
import 'package:shared_preferences/shared_preferences.dart'; // Import for SharedPreferences
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'firebase_options.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

import 'theme.dart';
import 'services/database_service.dart';
import 'services/firebase_auth_service.dart';
import 'services/message_service.dart';
// import 'services/update_service.dart'; // Removed - using Firebase services instead
// import 'services/device_subscription_service.dart'; // Removed - using FirebaseSubscriptionService instead
import 'services/firebase_messaging_service.dart'; // Import Firebase Messaging service
import 'services/firebase_in_app_messaging_service.dart'; // Import Firebase In-App Messaging service
import 'models/device_subscription_model.dart'; // Import the device subscription model
import 'package:isp_manager/pages/firebase_auth_page.dart';
import 'pages/dashboard_page.dart';
import 'pages/renew_subscription_page.dart'; // Import RenewSubscriptionPage
import 'services/subscription_check_service.dart'; // Add this import
import 'package:audioplayers/audioplayers.dart'; // Add for sound
import 'services/firebase_update_service.dart';

// مراقب دورة حياة التطبيق
class AppLifecycleObserver extends WidgetsBindingObserver {
  final VoidCallback onResumed;

  AppLifecycleObserver({required this.onResumed});

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      onResumed();
    }
  }
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase with the correct options
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('==== [MAIN] Firebase initialized successfully ====');
  } catch (e) {
    print('==== [MAIN] Firebase initialization failed: $e ====');
  }

  // طلب إذن الإشعارات مباشرة بعد تهيئة Firebase
  try {
    final messaging = FirebaseMessaging.instance;
    await messaging.requestPermission();
    print('==== [MAIN] Notification permission requested ====');
    // يمكنك الاشتراك في موضوع عام إذا أردت
    await messaging.subscribeToTopic('all');
    print('==== [MAIN] Subscribed to topic: all ====');
  } catch (e) {
    print('==== [MAIN] Error requesting notification permission or subscribing to topic: $e ====');
  }

  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  }

  // Firebase is already initialized above

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'نظام إدارة مشتركي الإنترنت',
      theme: lightTheme,
      darkTheme: darkTheme,
      themeMode: ThemeMode.system,
      debugShowCheckedModeBanner: false,
      home: const AppInitializer(),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [Locale('ar'), Locale('en')],
    );
  }
}

class AppInitializer extends StatefulWidget {
  const AppInitializer({super.key});

  @override
  State<AppInitializer> createState() => _AppInitializerState();
}
late FirebaseSubscriptionService deviceSubscriptionService; // Declare service
class _AppInitializerState extends State<AppInitializer> {
  bool _isInitializing = true;
  bool _isLoggedIn = false;
  bool _isSubscriptionExpired = false;
  DeviceSubscription? _deviceSubscription;
  final LocalAuthentication _localAuth = LocalAuthentication();
  final FirebaseAuthService _authService = FirebaseAuthService();
  Timer? _subscriptionCheckTimer; // Add timer for background check
  final AudioPlayer _audioPlayer = AudioPlayer(); // For playing alert sound
  bool _updateCheckedThisSession = false; // Prevent duplicate dialogs

  @override
  void initState() {
    super.initState();
    print('[DEBUG] AppInitializerState.initState called...');
    _initializeApp();
    _setupAuthStateListener();
    _setupAppLifecycleListener();
    _startBackgroundSubscriptionCheck(); // Start periodic check
  }

  @override
  void dispose() {
    _subscriptionCheckTimer?.cancel(); // Cancel timer on dispose
    WidgetsBinding.instance.removeObserver(AppLifecycleObserver(onResumed: () {}));
    super.dispose();
  }

  void _startBackgroundSubscriptionCheck() {
    _subscriptionCheckTimer?.cancel();
    _subscriptionCheckTimer = Timer.periodic(const Duration(seconds: 10), (timer) async {
      if (!_isInitializing && _isLoggedIn && mounted) {
        await _checkSubscriptionAndBlock();
      }
    });
  }

  void _setupAppLifecycleListener() {
    WidgetsBinding.instance.addObserver(AppLifecycleObserver(
      onResumed: () async {
        print('🔄 التطبيق عاد للمقدمة - إعادة فحص الاشتراك');
        if (_isLoggedIn && !_isInitializing && mounted) {
          await _checkSubscriptionAndBlock();
        }
      },
    ));
  }

  Future<void> _checkSubscriptionAndBlock() async {
    final prevExpired = _isSubscriptionExpired;
    final result = await SubscriptionCheckService.globalCheck(context);
    final DeviceSubscription? subscription = result['subscription'];
    final bool isValid = result['isValid'] ?? false;
    final bool nowExpired = !isValid;
    
    if (!mounted) return;
    setState(() {
      _deviceSubscription = subscription;
      _isSubscriptionExpired = nowExpired;
    });
    
    // Play sound only if just transitioned to expired
    if (!prevExpired && nowExpired) {
      _playSubscriptionExpiredSound();
    }
  }

  Future<void> _playSubscriptionExpiredSound() async {
    try {
      await _audioPlayer.play(AssetSource('error.mp3'));
    } catch (e) {
      print('Error playing alert sound: $e');
    }
  }

  void _setupAuthStateListener() {
    _authService.authStateChanges.listen((dynamic user) {
      print('🔄 تغيير في حالة المصادقة: ${user?.email ?? 'لا يوجد مستخدم'}');
      if (mounted) {
        setState(() {
          _isLoggedIn = user != null;
        });
        if (_isLoggedIn && !_updateCheckedThisSession) {
          print('[DEBUG] Calling _checkUpdateBlocking after login...');
          _updateCheckedThisSession = true;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _checkUpdateBlocking();
          });
        }
      }
    });
  }

  /// فحص البيانات في Firebase
  Future<void> _runBackgroundMigration() async {
    try {
      print('[FIREBASE] فحص البيانات في Firebase...');
      
      // التحقق من وجود بيانات في Firebase
      final firebaseCount = await deviceSubscriptionService.getSubscriptionsByAdmin('admin').then((list) => list.length);
      
      if (firebaseCount == 0) {
        print('[FIREBASE] لا توجد بيانات في Firebase، سيتم إنشاء اشتراكات جديدة عند الحاجة');
      } else {
        print('[FIREBASE] البيانات موجودة بالفعل في Firebase ($firebaseCount اشتراك)');
      }
    } catch (e) {
      print('[FIREBASE] خطأ في فحص البيانات: $e');
    }
  }

  Future<void> _initializeApp() async {
    print('[DEBUG] _initializeApp called...');
    try {
      final dbService = DatabaseService();
      final messageService = MessageService();
      final prefs = await SharedPreferences.getInstance();
      deviceSubscriptionService = FirebaseSubscriptionService(prefs);
      await FirebaseMessagingService().initialize();
      final firebaseInAppMessagingService = FirebaseInAppMessagingService();
      await firebaseInAppMessagingService.initialize();
      await firebaseInAppMessagingService.triggerAppOpen();
      await dbService.migrateToSQLite();
      await dbService.initializeDatabase();
      await messageService.createDefaultTemplates();
      
      // فحص البيانات في Firebase
      _runBackgroundMigration();
      bool isLoggedIn = await _authService.isUserAuthenticated();
      final firebaseUser = _authService.currentUser;
      if (isLoggedIn) {
        final bool rememberMe = prefs.getBool('remember_me') ?? true;
        if (!rememberMe) {
          await _authService.signOut();
          await dbService.setCurrentUser(null);
          await prefs.remove('user_email');
          await prefs.remove('login_timestamp');
          isLoggedIn = false;
        }
      }
      DeviceSubscription? subscription;
      bool isSubscriptionExpired = true;
      if (isLoggedIn) {
        final bool isBiometricEnabled = prefs.getBool('isBiometricEnabled') ?? false;
        if (isBiometricEnabled) {
          final bool didAuthenticate = await _localAuth.authenticate(
            localizedReason: 'يرجى المصادقة للدخول إلى التطبيق',
            options: const AuthenticationOptions(stickyAuth: true, biometricOnly: true),
          );
          if (!didAuthenticate) {
            await _authService.signOut();
            isLoggedIn = false;
          }
        }
        if (isLoggedIn) {
          final userData = await _authService.getUserData(firebaseUser!.uid);
          if (userData != null) {
            await dbService.setCurrentUser(userData);
          }
          // Use the new centralized check
          final result = await SubscriptionCheckService.globalCheck(context);
          subscription = result['subscription'];
          isSubscriptionExpired = !(result['isValid'] ?? false);
        }
      }
      print('📊 ملخص حالة التطبيق:');
      print('   🔐 مسجل دخول: $isLoggedIn');
      print('   📅 الاشتراك منتهي: $isSubscriptionExpired');
      
      if (!mounted) return;
      setState(() {
        _isLoggedIn = isLoggedIn;
        _deviceSubscription = subscription;
        _isSubscriptionExpired = isSubscriptionExpired;
        _isInitializing = false;
      });
      // After initialization and subscription check, check for updates
      WidgetsBinding.instance.addPostFrameCallback((_) {
        print('[DEBUG] addPostFrameCallback for _checkUpdateBlocking...');
        if (!_updateCheckedThisSession) {
          _updateCheckedThisSession = true;
          _checkUpdateBlocking();
        }
      });
    } catch (e) {
      print('Error initializing app: $e');
      if (!mounted) return;
      setState(() {
        _isInitializing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    print('[DEBUG] AppInitializer build called...');
    if (_isInitializing) {
      return Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).colorScheme.primary.withOpacity(0.1),
                Theme.of(context).colorScheme.surface,
              ],
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).colorScheme.primary,
                  ),
                ),
                SizedBox(height: 24),
                Text(
                  'جاري تهيئة التطبيق...',
                  style: TextStyle(
                    fontSize: 16,
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'فحص التحديثات...',
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withOpacity(0.5),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }
    // Block the app if subscription is expired
    if (_isLoggedIn && _isSubscriptionExpired) {
      return SubscriptionCheckService.expiredBlockingWidget(_deviceSubscription, context);
    }
    return _getHomePage();
  }

  // إنشاء اشتراك وهمي في حالة عدم وجود اشتراك
  DeviceSubscription _createDummySubscription() {
    final now = DateTime.now();
    return DeviceSubscription(
      id: 'dummy-id',
      createdAt: now,
      updatedAt: now,
      deviceId: 'unknown-device',
      accountNumber: 'غير محدد',
      subscriptionStartDate: now.subtract(const Duration(days: 30)),
      subscriptionEndDate: now.subtract(const Duration(days: 1)), // منتهي الصلاحية
      isActive: false,
      lastAccess: now,
      accessCount: 0,
    );
  }

  // تحديد الصفحة الرئيسية بناءً على حالة المستخدم والاشتراك
  Widget _getHomePage() {
    print('🏠 تحديد الصفحة الرئيسية...');
    print('🔐 مسجل دخول: $_isLoggedIn');
    print('📅 الاشتراك منتهي: $_isSubscriptionExpired');

    // إذا لم يكن مسجل دخول
    if (!_isLoggedIn) {
      print('→ توجيه لصفحة تسجيل الدخول');
      return const FirebaseAuthPage();
    }

    // إذا كان مسجل دخول لكن الاشتراك منتهي
    if (_isSubscriptionExpired) {
      print('→ توجيه لصفحة تجديد الاشتراك');
      return RenewSubscriptionPage(
        deviceSubscription: _deviceSubscription ?? _createDummySubscription(),
      );
    }

    // إذا كان مسجل دخول والاشتراك نشط
    print('→ توجيه للوحة الرئيسية');
    return const DashboardPage();
  }

  // التحقق من حالة التحديث المطلوب
  Future<bool> _checkIfUpdateStillRequired() async {
    try {
      final updateService = FirebaseUpdateService();
      return await updateService.isUpdateRequired();
    } catch (e) {
      print('خطأ في فحص حالة التحديث: $e');
      return false; // في حالة الخطأ، السماح بالوصول للتطبيق
    }
  }

  // Add update check logic compatible with new structure
  Future<void> _checkUpdateBlocking() async {
    print('[DEBUG] _checkUpdateBlocking called...');
    try {
      print('بدء فحص التحديث الإجباري...');
      final updateService = FirebaseUpdateService();
      final bool updateRequired = await updateService.isUpdateRequired();
      print('نتيجة فحص التحديث النهائية: $updateRequired');
      if (updateRequired) {
        print('يتوفر تحديث - عرض نافذة التحديث');
        final latestVersionData = await updateService.fetchLatestVersion();
        if (latestVersionData != null) {
          // إظهار نافذة التحديث وحجب باقي التطبيق
          await updateService.showForcedUpdateDialog(context);
        } else {
          print('خطأ: لا توجد بيانات تحديث متاحة');
        }
      } else {
        print('لا يوجد تحديث مطلوب - السماح بالوصول للتطبيق');
      }
    } catch (e) {
      print('خطأ في فحص التحديث: $e');
    }
  }
}
