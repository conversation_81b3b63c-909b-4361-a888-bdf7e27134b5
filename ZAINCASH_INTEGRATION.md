# دليل تكامل بوابة دفع زين كاش

## نظرة عامة
تم تكامل بوابة دفع زين كاش في تطبيق ISP Manager وفقاً للمواصفات الرسمية لـ ZainCash API v1.0 لتمكين المستخدمين من تجديد اشتراكاتهم بسهولة وأمان.

## المواصفات المتبعة
- **ZainCash API Version**: 1.0
- **Test API**: test.zaincash.iq
- **Live API**: api.zaincash.iq
- **الحد الأدنى للمبلغ**: 250 دينار عراقي
- **انتهاء صلاحية التوكن**: 4 ساعات

## الميزات المضافة

### 1. خدمة ZainCash (`lib/services/zaincash_service.dart`)
- إنشاء طلبات الدفع
- التحقق من حالة المدفوعات
- تفعيل الاشتراكات تلقائياً
- حفظ تاريخ المعاملات محلياً
- تنظيف المعاملات القديمة

### 2. صفحة تجديد الاشتراك المحدثة (`lib/pages/renew_subscription_page.dart`)
- خيارات دفع متعددة (زين كاش + واتساب)
- واجهة مستخدم محسنة
- معالجة أخطاء شاملة
- تتبع حالة المعاملات

### 3. صفحة تاريخ المدفوعات (`lib/pages/payment_history_page.dart`)
- عرض جميع المعاملات السابقة
- التحقق من حالة المعاملات المعلقة
- واجهة مستخدم بديهية

## بيانات الاعتماد

### بيانات الإنتاج (Live) - مُكوّنة ✅
```dart
Merchant ID: 5eba52ff3924b2df06877ddc
Secret: $2y$10$dozRcLK6VKjL7L19uXhVdeH7hP/RF65vNeL9w/tC/Am073TaBBwey
Phone Number: 9647819597948
```
**ملاحظة**: هذه بياناتك الحقيقية وقد تم تكوينها في التطبيق.

### بيانات الاختبار (Test) - متوفرة في الكود
```dart
Merchant ID: 5ffacf6612b5777c6d44266f
Secret: $2y$10$hBbAZo2GfSSvyqAyV2SaqOfYewgYpfR1O19gIh4SqyGWdmySZYPuS
Phone Number: 9647835077893

// بيانات عميل الاختبار
Customer MSISDN: 9647802999569
Customer PIN: 1234
Customer OTP: 1111
```

## كيفية الاستخدام

### للمستخدمين:
1. اختر الباقة المناسبة
2. اضغط على "شراء"
3. اختر "زين كاش" كطريقة دفع
4. أكمل عملية الدفع في المتصفح
5. ارجع للتطبيق واضغط "التحقق من الدفع"
6. سيتم تفعيل الاشتراك تلقائياً

### للمطورين:

#### إنشاء طلب دفع:
```dart
final zainCashService = ZainCashService();
final result = await zainCashService.createPaymentRequest(
  packageId: 'package_id',
  accountNumber: 'account_number',
  amount: 250.0, // بالدينار العراقي (الحد الأدنى 250)
  packageName: 'باقة شهرية',
  durationDays: 30,
);

// النتيجة ستحتوي على:
// - success: true/false
// - orderId: معرف الطلب
// - transactionId: معرف المعاملة من ZainCash
// - paymentUrl: رابط الدفع
```

#### التحقق من حالة الدفع:
```dart
final statusResult = await zainCashService.checkPaymentStatus(transactionId);
if (statusResult['success'] && statusResult['paid']) {
  // الدفع مكتمل
}
```

#### تفعيل الاشتراك:
```dart
final activationResult = await zainCashService.activateSubscription(
  orderId: orderId,
  transactionId: transactionId,
);
```

#### معالجة الـ Redirect Token:
```dart
// عند استلام token من redirect URL
final redirectData = zainCashService.handleRedirectToken(token);
if (redirectData != null) {
  final status = redirectData['status']; // success, failed, pending
  final orderId = redirectData['orderid'];
  final transactionId = redirectData['id'];

  if (status == 'success') {
    // الدفع نجح - تفعيل الاشتراك
    await zainCashService.activateSubscription(
      orderId: orderId,
      transactionId: transactionId,
    );
  }
}
```

#### استخدام بيانات الاختبار:
```dart
// للحصول على بيانات الاختبار
final testCredentials = ZainCashService.getTestCredentials();
print('Test Merchant ID: ${testCredentials['merchantId']}');

// تفعيل وضع الاختبار
ZainCashService.setTestMode(true);
```

## الأمان

### JWT Token
- يتم إنشاء JWT token لكل طلب
- التوقيع باستخدام HMAC-SHA256
- انتهاء صلاحية التوكن خلال ساعة واحدة

### تشفير البيانات
- جميع الطلبات مشفرة باستخدام HTTPS
- بيانات المعاملات محفوظة محلياً بشكل آمن
- لا يتم حفظ بيانات الدفع الحساسة

## معالجة الأخطاء

### أخطاء شائعة:
1. **فشل الاتصال**: التحقق من الاتصال بالإنترنت
2. **توكن منتهي الصلاحية**: إعادة إنشاء الطلب
3. **بيانات غير صحيحة**: التحقق من صحة المدخلات
4. **دفع معلق**: انتظار إكمال المعاملة

### رسائل الخطأ:
- جميع الأخطاء معروضة باللغة العربية
- تفاصيل تقنية في console للمطورين
- إرشادات واضحة للمستخدمين

## التحديثات المستقبلية

### مخطط لها:
- [ ] دعم طرق دفع إضافية
- [ ] تقارير مفصلة للمدفوعات
- [ ] إشعارات push للمعاملات
- [ ] نظام استرداد تلقائي

### تحسينات ممكنة:
- [ ] cache للمعاملات
- [ ] sync مع قاعدة البيانات السحابية
- [ ] تحليلات المدفوعات
- [ ] نظام خصومات

## الدعم الفني

### ملفات مهمة:
- `lib/services/zaincash_service.dart` - الخدمة الرئيسية
- `lib/pages/renew_subscription_page.dart` - واجهة التجديد
- `lib/pages/payment_history_page.dart` - تاريخ المدفوعات

### تسجيل الأخطاء:
جميع العمليات مسجلة في console باستخدام `debugPrint()` لسهولة التتبع والصيانة.

### اختبار التكامل:
1. اختبار إنشاء طلب دفع
2. اختبار التحقق من الحالة
3. اختبار تفعيل الاشتراك
4. اختبار معالجة الأخطاء

## ملاحظات مهمة

⚠️ **تحذير**: تأكد من تحديث `_redirectUrl` في `zaincash_service.dart` ليشير إلى رابط تطبيقك الفعلي.

✅ **نصيحة**: استخدم بيئة الاختبار أولاً قبل التشغيل الفعلي.

🔒 **أمان**: لا تشارك بيانات الاعتماد في الكود المصدري العام.

📱 **UX**: تأكد من وضوح تعليمات الدفع للمستخدمين.

## ⚠️ ملاحظات مهمة:

1. **تحديث الرابط**: غيّر `redirectUrl` في `lib/config/zaincash_config.dart` ليشير لرابط تطبيقك
2. **التبديل بين البيئات**:
   - للاختبار: غيّر `isProduction = false` في `ZainCashConfig`
   - للإنتاج: غيّر `isProduction = true` في `ZainCashConfig`
3. **الأمان**: بيانات الاعتماد محفوظة بشكل آمن في ملف التكوين
4. **التحقق من الإعدادات**: الخدمة تتحقق تلقائياً من صحة الإعدادات عند التشغيل

## 🔧 ملف التكوين الجديد

تم إنشاء ملف `lib/config/zaincash_config.dart` لإدارة جميع إعدادات ZainCash:

```dart
// للتبديل بين بيئة الاختبار والإنتاج
static const bool isProduction = true; // غيّر إلى false للاختبار

// تحديث رابط الإعادة التوجيه
static const String redirectUrl = 'https://your-app.com/payment-success';
```

### مزايا ملف التكوين:
- ✅ إدارة مركزية للإعدادات
- ✅ سهولة التبديل بين البيئات
- ✅ رسائل خطأ موحدة
- ✅ التحقق التلقائي من صحة الإعدادات
- ✅ دعم بيانات الاختبار الرسمية
