import 'package:flutter/material.dart';
import '../models/subscriber_model.dart';
import '../models/message_template_model.dart';
import '../models/package_model.dart';
import '../services/database_service.dart';
import '../services/app_settings_service.dart';
import '../services/whatsapp_service.dart';
import '../services/message_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:android_intent_plus/android_intent.dart';
import 'message_templates_page.dart';

class DebtReminderPage extends StatefulWidget {
  const DebtReminderPage({super.key});

  @override
  State<DebtReminderPage> createState() => _DebtReminderPageState();
}

class _DebtReminderPageState extends State<DebtReminderPage> {
  List<SubscriberModel> _allDebtors = [];
  List<SubscriberModel> _filteredDebtors = [];
  List<String> _selectedIds = [];
  bool _autoRepeat = false;
  TimeOfDay _selectedTime = const TimeOfDay(hour: 8, minute: 0);
  int _currentSendingIndex = -1; // -1: لا يوجد إرسال حالي
  bool _isSending = false;
  bool _sendViaSms = false; // خيار الإرسال عبر SMS
  bool _isLoading = false;
  
  // قوالب الرسائل المخصصة
  List<MessageTemplateModel> _reminderTemplates = [];
  MessageTemplateModel? _selectedTemplate;
  
  // تحسينات جديدة
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  double _minDebtFilter = 0;
  double _maxDebtFilter = double.infinity;
  String _sortBy = 'name'; // name, debt_asc, debt_desc
  List<String> _sentToday = []; // قائمة المرسل إليهم اليوم
    @override
  void initState() {
    super.initState();
    _fetchDebtors();
    _loadSettings();
    _loadSentToday();
    _loadAppSettings();
    _loadMessageTemplates();
  }

  Future<void> _loadAppSettings() async {
    try {
      setState(() {
      });
    } catch (e) {
      // في حالة الخطأ، استخدم القيم الافتراضية
      setState(() {
      });
    }
  }

  Future<void> _loadMessageTemplates() async {
    try {
      final templates = await DatabaseService().getMessageTemplatesFire();
      final reminderTemplates = templates.where((t) => t.type == MessageTemplateType.reminder).toList();
      
      setState(() {
        _reminderTemplates = reminderTemplates;
        // اختيار أول قالب كافتراضي إذا لم يكن هناك قالب محدد
        if (_selectedTemplate == null && reminderTemplates.isNotEmpty) {
          _selectedTemplate = reminderTemplates.first;
        }
      });
    } catch (e) {
      print('خطأ في تحميل قوالب الرسائل: $e');
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _fetchDebtors() async {
    if (!mounted) return;
    setState(() => _isLoading = true);
    try {
      final debtors = await DatabaseService().getDebtors();
      if (!mounted) return;
      setState(() {
        _allDebtors = debtors;
        _filteredDebtors = debtors;
        _selectedIds = debtors.map((e) => e.id).toList();
      });
      _applyFiltersAndSort();
    } catch (e) {
      _showErrorDialog('خطأ في تحميل البيانات', 'خطأ في تحميل البيانات: $e');
    } finally {
      if (!mounted) return;
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    if (!mounted) return;
    setState(() {
      _autoRepeat = prefs.getBool('debt_reminder_auto_repeat') ?? false;
      final hour = prefs.getInt('debt_reminder_hour') ?? 8;
      final minute = prefs.getInt('debt_reminder_minute') ?? 0;
      _selectedTime = TimeOfDay(hour: hour, minute: minute);
      _sendViaSms = prefs.getBool('debt_reminder_sms') ?? false;
    });
    
    // تحميل القالب المحفوظ
    final savedTemplateId = prefs.getString('debt_reminder_template_id');
    if (savedTemplateId != null && _reminderTemplates.isNotEmpty) {
      final savedTemplate = _reminderTemplates.where((t) => t.id == savedTemplateId).firstOrNull;
      if (savedTemplate != null && mounted) {
        setState(() {
          _selectedTemplate = savedTemplate;
        });
      }
    }
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('debt_reminder_auto_repeat', _autoRepeat);
    await prefs.setInt('debt_reminder_hour', _selectedTime.hour);
    await prefs.setInt('debt_reminder_minute', _selectedTime.minute);
    await prefs.setBool('debt_reminder_sms', _sendViaSms);
    
    // حفظ القالب المحدد
    if (_selectedTemplate != null) {
      await prefs.setString('debt_reminder_template_id', _selectedTemplate!.id);
    }
  }

  Future<void> _loadSentToday() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now().toIso8601String().split('T')[0];
    final sentKey = 'debt_reminder_sent_$today';
    final sent = prefs.getStringList(sentKey) ?? [];
    if (!mounted) return;
    setState(() {
      _sentToday = sent;
    });
  }

  Future<void> _saveSentToday(String subscriberId) async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now().toIso8601String().split('T')[0];
    final sentKey = 'debt_reminder_sent_$today';
    _sentToday.add(subscriberId);
    await prefs.setStringList(sentKey, _sentToday);
  }

  void _applyFiltersAndSort() {
    List<SubscriberModel> filtered = _allDebtors.where((debtor) {
      // تصفية بالبحث
      bool matchesSearch = _searchQuery.isEmpty ||
          debtor.fullName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          debtor.phoneNumber.contains(_searchQuery);
      
      // تصفية بالدين
      bool matchesDebt = debtor.debtAmount >= _minDebtFilter &&
          debtor.debtAmount <= _maxDebtFilter;
      
      return matchesSearch && matchesDebt;
    }).toList();

    // ترتيب
    switch (_sortBy) {
      case 'debt_desc':
        filtered.sort((a, b) => b.debtAmount.compareTo(a.debtAmount));
        break;
      case 'debt_asc':
        filtered.sort((a, b) => a.debtAmount.compareTo(b.debtAmount));
        break;
      default:
        filtered.sort((a, b) => a.fullName.compareTo(b.fullName));
    }

    setState(() {
      _filteredDebtors = filtered;
      // تحديث المحددين ليشمل فقط المفلترين
      _selectedIds = _selectedIds.where((id) => 
          _filteredDebtors.any((d) => d.id == id)).toList();
    });
  }

  void _toggleSelectAll(bool value) {
    setState(() {
      if (value) {
        _selectedIds = _filteredDebtors.map((e) => e.id).toList();
      } else {
        _selectedIds.clear();
      }
    });
  }

  void _toggleSelect(String id, bool value) {
    setState(() {
      if (value) {
        if (!_selectedIds.contains(id)) {
          _selectedIds.add(id);
        }
      } else {
        _selectedIds.remove(id);
      }
    });
  }
  Future<String> _buildMessage(SubscriberModel s) async {
    if (_selectedTemplate == null) {
      // إذا لم يكن هناك قالب محدد، استخدم رسالة افتراضية
      final formattedAmount = await AppSettingsService.formatCurrency(s.debtAmount);
      return 'عزيزي ${s.fullName}، لديك ديون مستحقة قدرها $formattedAmount. نرجو السداد في أقرب وقت لتجنب إيقاف الخدمة.';
    }

    // استخدام القالب المحدد مع MessageService
    try {
      // جلب باقة المشترك
      final packages = await DatabaseService().getPackagesFire();
      final package = packages.where((p) => p.id == s.packageId).firstOrNull;
      
      // إذا لم يتم العثور على الباقة، استخدم باقة افتراضية
      final defaultPackage = package ?? PackageModel(
        adminId: s.adminId,
        id: 'default',
        name: 'باقة افتراضية',
        price: 0.0,
        durationInDays: 30,
        speed: 'غير محدد',
        deviceCount: 1,
        createdAt: DateTime.now(),
        serverId: '',
      );

      // استخدام MessageService لمعالجة القالب
      return await MessageService().previewMessage(
        _selectedTemplate!.content,
        s,
        defaultPackage,
      );
    } catch (e) {
      // في حالة الخطأ، استخدم رسالة افتراضية
      final formattedAmount = await AppSettingsService.formatCurrency(s.debtAmount);
      return 'عزيزي ${s.fullName}، لديك ديون مستحقة قدرها $formattedAmount. نرجو السداد في أقرب وقت لتجنب إيقاف الخدمة.';
    }
  }  Future<void> _sendWhatsAppMessage(String phone, String message) async {
    try {      // استخدام خدمة تنسيق أرقام الهواتف من الإعدادات
      await WhatsAppService().sendMessage(phone, message);
    } catch (e) {
      Fluttertoast.showToast(msg: 'فشل فتح واتساب: $e');
      rethrow;
    }
  }

  Future<void> _sendSmsMessage(String phone, String message) async {
    try {
      // تنسيق رقم الهاتف مع مفتاح البلد من الإعدادات
      final formattedPhone = await AppSettingsService.formatPhoneNumber(phone);
      
      final intent = AndroidIntent(
        action: 'android.intent.action.SENDTO',
        data: 'sms:$formattedPhone',
        arguments: {'sms_body': message},
      );
      await intent.launch();
    } catch (e) {
      Fluttertoast.showToast(msg: 'فشل فتح تطبيق الرسائل: $e');
      rethrow;
    }
  }

  Future<void> _pickTime() async {
    final picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
    );
    if (picked != null && mounted) {
      setState(() => _selectedTime = picked);
      _saveSettings();
    }
  }

  Future<void> _showSendConfirmation() async {
    if (_selectedIds.isEmpty) {
      Fluttertoast.showToast(msg: 'يرجى تحديد المشتركين أولاً');
      return;
    }

    if (_reminderTemplates.isEmpty) {
      _showErrorDialog(
        'لا توجد قوالب رسائل متاحة',
        'يرجى إنشاء قوالب رسائل من شاشة إدارة قوالب الرسائل أولاً.',
      );
      return;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الإرسال'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('سيتم إرسال ${_selectedIds.length} رسالة'),
            Text('طريقة الإرسال: ${_sendViaSms ? "SMS" : "واتساب"}'),
            const SizedBox(height: 16),
            const Text('معاينة الرسالة:', style: TextStyle(fontWeight: FontWeight.bold)),
            if (_selectedTemplate != null) ...[
              const SizedBox(height: 4),
              Text(
                'القالب: ${_selectedTemplate!.name}',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: FutureBuilder<String>(
                future: _buildMessage(_filteredDebtors.first),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const CircularProgressIndicator();
                  } else if (snapshot.hasError) {
                    return Text('Error: ${snapshot.error}');
                  } else {
                    return Text(snapshot.data ?? '');
                  }
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('تأكيد الإرسال'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      _startSequentialSending();
    }
  }

  void _startSequentialSending() async {
    setState(() {
      _isSending = true;
      _currentSendingIndex = 0;
    });
    _sendToCurrent();
  }

  void _sendToCurrent() async {
    if (_currentSendingIndex < 0 || _currentSendingIndex >= _selectedIds.length) {
      setState(() {
        _isSending = false;
        _currentSendingIndex = -1;
      });
      _showCompletionDialog();
      return;
    }
    
    SubscriberModel? s;
    try {
      s = _allDebtors.firstWhere((d) => d.id == _selectedIds[_currentSendingIndex]);
    } catch (e) {
      s = null;
    }
    
    if (s == null) {
      setState(() {
        _currentSendingIndex++;
      });
      _sendToCurrent();
      return;
    }
    
    final msg = await _buildMessage(s); // Await the message
    try {
      if (_sendViaSms) {
        await _sendSmsMessage(s.phoneNumber, msg);
        await _saveSentToday(s.id);
        setState(() {
          _currentSendingIndex++;
        });
        // تأخير قصير بين رسائل SMS
        await Future.delayed(const Duration(milliseconds: 500));
        _sendToCurrent();
      } else {
        await _sendWhatsAppMessage(s.phoneNumber, msg);
        await _saveSentToday(s.id);
        // بعد العودة من واتساب، ينتظر المستخدم الضغط للمتابعة
      }
    } catch (e) {
      _showErrorDialog('فشل إرسال الرسالة', 'فشل إرسال الرسالة لـ ${s.fullName}: $e');
    }
  }

  void _nextSend() {
    setState(() {
      _currentSendingIndex++;
    });
    _sendToCurrent();
  }

  void _showCompletionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تم الإنجاز'),
        content: Text('تم إرسال جميع التذكيرات (${_selectedIds.length} رسالة)'),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String title, [String? message]) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message ?? title),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showTemplateSelectionDialog() {
    if (_reminderTemplates.isEmpty) {
      _showErrorDialog('لا توجد قوالب رسائل متاحة', 'يرجى إنشاء قوالب من شاشة إدارة قوالب الرسائل.');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار قالب الرسالة'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _reminderTemplates.length,
            itemBuilder: (context, index) {
              final template = _reminderTemplates[index];
              final isSelected = _selectedTemplate?.id == template.id;
              
              return Card(
                color: isSelected ? Colors.blue.shade50 : null,
                child: ListTile(
                  title: Text(template.name),
                  subtitle: Text(
                    template.content.length > 100 
                        ? '${template.content.substring(0, 100)}...' 
                        : template.content,
                  ),
                  trailing: isSelected ? const Icon(Icons.check, color: Colors.blue) : null,
                  onTap: () {
                    setState(() {
                      _selectedTemplate = template;
                    });
                    _saveSettings();
                    Navigator.pop(context);
                  },
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        double tempMinDebt = _minDebtFilter;
        double tempMaxDebt = _maxDebtFilter == double.infinity ? 1000000 : _maxDebtFilter;
        
        return StatefulBuilder(
          builder: (context, setDialogState) => AlertDialog(
            title: const Text('تصفية المشتركين'),
            content: Column(
              mainAxisSize: MainAxisSize.min,              children: [
                FutureBuilder<String>(
                  future: AppSettingsService.getCurrencySymbol(),
                  builder: (context, snapshot) {
                    return TextField(
                      decoration: InputDecoration(
                        labelText: 'الحد الأدنى للدين',
                        suffixText: snapshot.data ?? '',
                      ),
                      keyboardType: TextInputType.number,
                      onChanged: (value) {
                        tempMinDebt = double.tryParse(value) ?? 0;
                      },
                    );
                  },
                ),
                const SizedBox(height: 16),
                FutureBuilder<String>(
                  future: AppSettingsService.getCurrencySymbol(),
                  builder: (context, snapshot) {
                    return TextField(
                      decoration: InputDecoration(
                        labelText: 'الحد الأقصى للدين',
                        suffixText: snapshot.data ?? '',
                      ),
                      keyboardType: TextInputType.number,
                      onChanged: (value) {
                        tempMaxDebt = double.tryParse(value) ?? double.infinity;
                      },
                    );
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _minDebtFilter = tempMinDebt;
                    _maxDebtFilter = tempMaxDebt;
                  });
                  _applyFiltersAndSort();
                  Navigator.pop(context);
                },
                child: const Text('تطبيق'),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatsCard() {
    final totalDebt = _filteredDebtors.fold<double>(0, (sum, s) => sum + s.debtAmount);
    final selectedDebt = _filteredDebtors
        .where((s) => _selectedIds.contains(s.id))
        .fold<double>(0, (sum, s) => sum + s.debtAmount);
    final sentTodayCount = _filteredDebtors
        .where((s) => _sentToday.contains(s.id))
        .length;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildStatItem('إجمالي المدينين', '${_filteredDebtors.length}'),
                _buildStatItem('المحددين', '${_selectedIds.length}'),
                _buildStatItem('تم الإرسال اليوم', '$sentTodayCount'),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildStatItem('إجمالي الديون', '${totalDebt.toStringAsFixed(0)} د'),
                _buildStatItem('ديون المحددين', '${selectedDebt.toStringAsFixed(0)} د'),
                _buildStatItem('القوالب المتاحة', '${_reminderTemplates.length}'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(value, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تذكير الديون المستحقة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _fetchDebtors,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // شريط الإحصائيات
                  _buildStatsCard(),
                  const SizedBox(height: 16),
                  
                  if (_isSending) ...[
                    // شريط التقدم
                    Card(
                      color: Colors.blue[50],
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          children: [
                            LinearProgressIndicator(
                              value: (_currentSendingIndex + 1) / _selectedIds.length,
                            ),
                            const SizedBox(height: 8),
                            Text('جاري الإرسال: ${_currentSendingIndex + 1} من ${_selectedIds.length}'),
                            if (!_sendViaSms) ...[
                              const SizedBox(height: 8),
                              ElevatedButton.icon(
                                icon: const Icon(Icons.navigate_next),
                                label: Text(_currentSendingIndex + 1 < _selectedIds.length ? 'التالي' : 'إنهاء'),
                                onPressed: _nextSend,
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                  
                  if (!_isSending) ...[
                    // شريط البحث والتصفية
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _searchController,
                            decoration: const InputDecoration(
                              hintText: 'البحث بالاسم أو رقم الهاتف...',
                              prefixIcon: Icon(Icons.search),
                              border: OutlineInputBorder(),
                            ),
                            onChanged: (value) {
                              if (!mounted) return;
                              setState(() => _searchQuery = value);
                              _applyFiltersAndSort();
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          icon: const Icon(Icons.filter_list),
                          onPressed: _showFilterDialog,
                        ),
                        PopupMenuButton<String>(
                          icon: const Icon(Icons.sort),
                          onSelected: (value) {
                            if (!mounted) return;
                            setState(() => _sortBy = value);
                            _applyFiltersAndSort();
                          },
                          itemBuilder: (context) => [
                            const PopupMenuItem(value: 'name', child: Text('ترتيب بالاسم')),
                            const PopupMenuItem(value: 'debt_desc', child: Text('الأعلى ديناً')),
                            const PopupMenuItem(value: 'debt_asc', child: Text('الأقل ديناً')),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    // أزرار التحديد وخيارات الإرسال
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // أزرار التحديد
                        Row(
                          children: [
                            Expanded(
                              child: ElevatedButton(
                                onPressed: () => _toggleSelectAll(true),
                                child: const Text('تحديد الكل'),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: ElevatedButton(
                                onPressed: () => _toggleSelectAll(false),
                                child: const Text('إلغاء الكل'),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        // خيارات الإرسال
                        Row(
                          children: [
                            Switch(
                              value: _sendViaSms,
                              onChanged: (v) {
                                if (!mounted) return;
                                setState(() => _sendViaSms = v);
                                _saveSettings();
                              },
                            ),
                            const SizedBox(width: 8),
                            Text(_sendViaSms ? 'SMS' : 'واتساب'),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    
                    // قائمة المشتركين
                    Expanded(
                      child: ListView.builder(
                        itemCount: _filteredDebtors.length,
                        itemBuilder: (context, index) {
                          final s = _filteredDebtors[index];
                          final isSelected = _selectedIds.contains(s.id);
                          final isSentToday = _sentToday.contains(s.id);
                          
                          return Card(
                            color: isSentToday ? Colors.green[50] : null,
                            child: CheckboxListTile(
                              value: isSelected,
                              onChanged: (v) => _toggleSelect(s.id, v ?? false),
                              title: Text(s.fullName),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('الهاتف: ${s.phoneNumber}'),
                                  FutureBuilder<String>(
                                    future: AppSettingsService.formatCurrency(s.debtAmount),
                                    builder: (context, snapshot) {
                                      return Text('الدين: ${snapshot.data ?? '...'}');
                                    },
                                  ),
                                  if (isSentToday)
                                    const Text('✓ تم الإرسال اليوم', 
                                        style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold)),
                                ],
                              ),
                              isThreeLine: true,
                            ),
                          );
                        },
                      ),
                    ),
                    
                    // بطاقة اختيار القالب (انقلها هنا)
                    Card(
                      margin: const EdgeInsets.symmetric(vertical: 8),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // العنوان
                            Row(
                              children: [
                                const Icon(Icons.message, color: Colors.blue),
                                const SizedBox(width: 8),
                                const Text(
                                  'قالب الرسالة:',
                                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            
                            // عرض القالب المحدد
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.grey[50],
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.grey[300]!),
                              ),
                              child: _selectedTemplate != null
                                  ? Text(
                                      'القالب: ${_selectedTemplate!.name}',
                                      style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 15),
                                    )
                                  : const Text(
                                      'لم يتم اختيار قالب. سيتم استخدام رسالة افتراضية.',
                                      style: TextStyle(color: Colors.orange, fontSize: 14),
                                    ),
                            ),
                            const SizedBox(height: 12),
                            
                            // أزرار التحكم
                            Row(
                              children: [
                                if (_selectedTemplate != null) ...[
                                  Expanded(
                                    child: TextButton.icon(
                                      onPressed: () {
                                        showDialog(
                                          context: context,
                                          builder: (context) => AlertDialog(
                                            title: Text('معاينة القالب: ${_selectedTemplate!.name}'),
                                            content: SingleChildScrollView(
                                              child: Text(_selectedTemplate!.content),
                                            ),
                                            actions: [
                                              TextButton(
                                                onPressed: () => Navigator.pop(context),
                                                child: const Text('إغلاق'),
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                      icon: const Icon(Icons.visibility, size: 18),
                                      label: const Text('معاينة'),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                ],
                                Expanded(
                                  child: TextButton.icon(
                                    onPressed: _showTemplateSelectionDialog,
                                    icon: const Icon(Icons.edit, size: 18),
                                    label: const Text('اختيار قالب'),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: TextButton.icon(
                                    onPressed: () async {
                                      await Navigator.of(context).push(
                                        MaterialPageRoute(builder: (context) => const MessageTemplatesPage()),
                                      );
                                      await _loadMessageTemplates();
                                    },
                                    icon: const Icon(Icons.settings, size: 16),
                                    label: const Text('إدارة القوالب'),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    // زر الإرسال
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.send),
                        label: const Text('إرسال التذكير الآن'),
                        onPressed: _selectedIds.isEmpty ? null : _showSendConfirmation,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.all(16),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
    );
  }
}
