import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:isp_manager/services/firebase_auth_service.dart';
import 'package:isp_manager/services/firebase_service.dart';
import 'package:path/path.dart' as path_lib;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:share_plus/share_plus.dart'; // Import share_plus
import 'package:path_provider/path_provider.dart'; // For temporary directory
import '../models/user_model.dart';
import '../models/package_model.dart';
import '../models/subscriber_model.dart';
import '../models/message_template_model.dart';
import '../models/expense_category_model.dart'; // New import
import '../models/expense_model.dart'; // New import
import '../models/activity_log_model.dart'; // Add ActivityLogModel import
import '../models/payment_record_model.dart';
import 'sqlite_service.dart';
import 'telegram_service.dart'; // Import TelegramService
import 'sas_api_service.dart'; // Import SasApiService
import '../models/sas_user_model.dart'; // Import SasUser
import 'app_settings_service.dart'; // Add AppSettingsService

class DatabaseService {
  static const String _usersKey = 'users';
  static const String _packagesKey = 'packages';
  static const String _subscribersKey = 'subscribers';
  static const String _activityLogsKey = 'activity_logs';
  static const String _paymentRecordsKey = 'payment_records';
  static const String _messageTemplatesKey = 'message_templates';
  static const String _expenseCategoriesKey =
      'expense_categories'; // New constant
  static const String _expensesKey = 'expenses'; // New constant
  static const String _currentUserKey = 'current_user';
  static const String _isInitializedKey = 'is_initialized';

  static final DatabaseService _instance = DatabaseService._internal();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final authService = FirebaseAuthService();
  final firebaseUser = "";
  factory DatabaseService() => _instance;
  DatabaseService._internal();
  final user = FirebaseAuth.instance.currentUser;
  late String adminId = "";
  final Uuid _uuid = const Uuid();
  final SQLiteService _sqliteService = SQLiteService();
  final SasApiService _sasApiService =
      SasApiService(); // Add SasApiService instance

  // Flag to determine if we should use SQLite or SharedPreferences
  bool _useSQLite = true;

  // Initialize database with sample data
  Future<void> initializeDatabase() async {
    if (_useSQLite) {
      // Check if database is already initialized by checking if users table has data
      final users = await _sqliteService.getUsers();
      if (users.isEmpty) {
        await _createSampleData();
      }
      // Ensure default expense categories are added if they don't exist
      final categories = await _sqliteService.getExpenseCategories();
      if (categories.isEmpty) {
        await _addDefaultExpenseCategories();
      }
    } else {
      // Legacy SharedPreferences initialization
      final prefs = await SharedPreferences.getInstance();
      final isInitialized = prefs.getBool(_isInitializedKey) ?? false;

      if (!isInitialized) {
        await _createSampleData();
        await prefs.setBool(_isInitializedKey, true);
      }
      // For SharedPreferences, we'll add default categories during sample data creation
    }
  }

  Future<void> loadAdmin() async {
    final doc = await FirebaseFirestore.instance
        .collection('users')
        .doc(FirebaseAuth.instance.currentUser!.uid)
        .get();
    adminId = doc["adminId"];
    print(doc["adminId"]);
    print(adminId);
  }

  Future<void> _createSampleData() async {
    // Create only admin user
    final users = [
      UserModel(
        adminId: _uuid.v4(),
        id: _uuid.v4(),
        username: 'admin',
        password: '123456',
        phoneNumber: '0791234567',
        role: UserRole.admin,
        fullName: 'المدير',
        createdAt: DateTime.now(),
        permissions: Permission.allPermissions,
      ),
    ];

    // Empty packages list
    final packages = <PackageModel>[];

    // Empty subscribers list
    final subscribers = <SubscriberModel>[];

    if (_useSQLite) {
      // Save to SQLite
      for (var user in users) {
        await _sqliteService.insertUser(user.toJson());
      }
      await _addDefaultExpenseCategories(); // Add default categories for SQLite
    } else {
      // Save to SharedPreferences
      await saveUsers(users);
      await savePackages(packages);
      await saveSubscribers(subscribers);
      await _addDefaultExpenseCategories(); // Add default categories for SharedPreferences
    }
  }

  Future<void> _addDefaultExpenseCategories() async {
    final defaultCategories = [
      'إيجار',
      'رواتب',
      'كهرباء',
      'ماء',
      'صيانة',
      'إنترنت',
      'مستلزمات مكتبية',
      'وقود',
      'ضرائب',
      'أخرى',
    ];

    for (var categoryName in defaultCategories) {
      // Check if category already exists to prevent duplicates on app restart
      final existingCategories = await getExpenseCategories();
      if (!existingCategories.any((cat) => cat.name == categoryName)) {
        final category = ExpenseCategoryModel(
          adminId: adminId,
          id: _uuid.v4(),
          name: categoryName,
          createdAt: DateTime.now(),
        );
        if (_useSQLite) {
          await _sqliteService.insertExpenseCategory(category.toJson());
        } else {
          final categories = await getExpenseCategories();
          categories.add(category);
          await saveExpenseCategories(categories);
        }
      }
    }
  }

  // User operations
  Future<List<UserModel>> getUsers() async {
    if (_useSQLite) {
      final userMaps = await _sqliteService.getUsers();
      return userMaps.map((map) => UserModel.fromJson(map)).toList();
    } else {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = prefs.getString(_usersKey);
      if (usersJson == null) return [];

      final List<dynamic> usersList = jsonDecode(usersJson);
      return usersList.map((json) => UserModel.fromJson(json)).toList();
    }
  }

  Future<void> saveUsers(List<UserModel> users) async {
    if (_useSQLite) {
      // Clear existing users and insert new ones
      await _sqliteService
          .clearAllTables(); // This is a bit extreme, consider more targeted approach
      for (var user in users) {
        await _sqliteService.insertUser(user.toJson());
      }
    } else {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = jsonEncode(users.map((user) => user.toJson()).toList());
      await prefs.setString(_usersKey, usersJson);
    }
  }

  Future<UserModel?> authenticate(String username, String password) async {
    if (_useSQLite) {
      // For SQLite, we need to query the database directly for better performance
      final db = await _sqliteService.database;
      final results = await db.query(
        'users',
        where:
            '(username = ? OR phoneNumber = ?) AND password = ? AND (isActive = 1 OR isActive = "true")',
        whereArgs: [username, username, password],
      );

      if (results.isNotEmpty) {
        return UserModel.fromJson(results.first);
      }
      return null;
    } else {
      // Legacy SharedPreferences approach
      final users = await getUsers();
      try {
        return users.firstWhere(
          (user) =>
              (user.username == username || user.phoneNumber == username) &&
              user.password == password &&
              user.isActive,
        );
      } catch (e) {
        return null;
      }
    }
  }

  Future<SubscriberModel?> getSubscriberById(String id) async {
    if (_useSQLite) {
      final subscriberMap = await _sqliteService.getSubscriberById(id);
      return subscriberMap != null
          ? SubscriberModel.fromJson(subscriberMap)
          : null;
    } else {
      final subscribers = await getSubscribers();
      try {
        return subscribers.firstWhere((s) => s.id == id);
      } catch (e) {
        return null;
      }
    }
  }

  Future<void> setCurrentUser(UserModel? user) async {
    // We'll keep using SharedPreferences for current user session regardless of database choice
    final prefs = await SharedPreferences.getInstance();
    if (user != null) {
      await prefs.setString(_currentUserKey, jsonEncode(user.toJson()));
    } else {
      await prefs.remove(_currentUserKey);
    }
  }

  Future<UserModel?> getCurrentUser() async {
    // We'll keep using SharedPreferences for current user session regardless of database choice
    final prefs = await SharedPreferences.getInstance();
    final userJson = prefs.getString(_currentUserKey);
    if (userJson == null) return null;
    return UserModel.fromJson(jsonDecode(userJson));
  }

  Future<void> updateUser(UserModel user) async {
    if (_useSQLite) {
      await _sqliteService.updateUser(user.toJson());
    } else {
      final users = await getUsers();
      final index = users.indexWhere((u) => u.id == user.id);
      if (index != -1) {
        users[index] = user;
        await saveUsers(users);
      }
    }
  }

  // Package operations
  Future<List<PackageModel>> getPackages() async {
    if (_useSQLite) {
      final packageMaps = await _sqliteService.getPackages();

      return packageMaps.map((map) => PackageModel.fromJson(map)).toList();
    } else {
      final prefs = await SharedPreferences.getInstance();
      final packagesJson = prefs.getString(_packagesKey);
      if (packagesJson == null) return [];

      final List<dynamic> packagesList = jsonDecode(packagesJson);
      return packagesList.map((json) => PackageModel.fromJson(json)).toList();
    }
  }

  Future<List<PackageModel>> getPackagesFire() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('packages')
          .where('adminId', isEqualTo: adminId)
          .get();

      return snapshot.docs.map((doc) {
        return PackageModel.fromMap(doc.data());
      }).toList();
    } catch (e) {
      print('Error fetching packages from Firestore: $e');
      return [];
    }
  }

  Future<void> syncPackagesToFirebase() async {
    final localPackages = await getPackages(); // من الدالة اللي عندك
    final firebaseSnapshot = await _firestore
        .collection('packages')
        .where('adminId', isEqualTo: adminId)
        .get();

    // IDs الموجودة بالفعل في Firestore
    final firebaseIds = firebaseSnapshot.docs.map((doc) => doc.id).toSet();

    for (final package in localPackages) {
      if (!firebaseIds.contains(package.id)) {
        PackageModel newPackage = package.copyWith(adminId: adminId);
        await _firestore
            .collection('packages')
            .doc(newPackage.id)
            .set(newPackage.toMap());
      }
    }
  }

  Future<void> syncTemplatesToFirebase() async {
    final localTemplates = await getMessageTemplates(); // من الدالة اللي عندك
    final firebaseSnapshot = await _firestore
        .collection('message_templates')
        .get();

    // IDs الموجودة بالفعل في Firestore
    final firebaseIds = firebaseSnapshot.docs.map((doc) => doc.id).toSet();

    for (final template in localTemplates) {
      if (!firebaseIds.contains(template.id)) {
        MessageTemplateModel newTemplate = template.copyWith(adminId: adminId);
        await _firestore
            .collection('message_templates')
            .doc(newTemplate.id)
            .set(newTemplate.toMap());
      }
    }
  }

  Future<void> syncExpensesToFirebase() async {
    final localExpenses = await getExpenses(); // من الدالة اللي عندك
    final firebaseSnapshot = await _firestore
        .collection('expenses')
        .where('adminId', isEqualTo: adminId)
        .get();

    // IDs الموجودة بالفعل في Firestore
    final firebaseIds = firebaseSnapshot.docs.map((doc) => doc.id).toSet();

    for (final expense in localExpenses) {
      if (!firebaseIds.contains(expense.id)) {
        ExpenseModel newEx = expense.copyWith(adminId: adminId);
        await _firestore
            .collection('expenses')
            .doc(newEx.id)
            .set(newEx.toMap());
      }
    }
  }

  Future<void> syncPaymentsToFirebase() async {
    final localPayments = await getPaymentRecords(); // من الدالة اللي عندك
    final firebaseSnapshot = await _firestore
        .collection('paymentRecord')
        .where('adminId', isEqualTo: adminId)
        .get();

    final firebaseIds = firebaseSnapshot.docs.map((doc) => doc.id).toSet();

    for (final payment in localPayments) {
      if (!firebaseIds.contains(payment.id)) {
        PaymentRecordModel newPayment = payment.copyWith(adminId: adminId);
        await _firestore
            .collection('paymentRecord')
            .doc(newPayment.id)
            .set(newPayment.toMap());
      }
    }
  }

  Future<void> syncActivityToFirebase() async {
    final localActivities = await getActivityLogs(); // من الدالة اللي عندك
    final firebaseSnapshot = await _firestore
        .collection('activityLog')
        .where('adminId', isEqualTo: adminId)
        .get();

    final firebaseIds = firebaseSnapshot.docs.map((doc) => doc.id).toSet();

    for (final activity in localActivities) {
      if (!firebaseIds.contains(activity.id)) {
        ActivityLogModel newActivity = activity.copyWith(adminId: adminId);
        await _firestore
            .collection('activityLog')
            .doc(newActivity.id)
            .set(newActivity.toMap());
      }
    }
  }

  Future<void> syncExpenseCategoryToFirebase() async {
    final localCategories = await getExpenseCategories(); // من الدالة اللي عندك
    final firebaseSnapshot = await _firestore
        .collection('expenseCategory')
        .get();

    final firebaseIds = firebaseSnapshot.docs.map((doc) => doc.id).toSet();

    for (final category in localCategories) {
      if (!firebaseIds.contains(category.id)) {
        ExpenseCategoryModel newCat = category.copyWith(adminId: adminId);
        await _firestore
            .collection('expenseCategory')
            .doc(newCat.id)
            .set(newCat.toMap());
      }
    }
  }

  Future<void> syncSubscribersToFirebase() async {
    final localSubscribers = await getSubscribers(); // من الدالة اللي عندك
    final firebaseSnapshot = await _firestore
        .collection('subscribers')
        .where('adminId', isEqualTo: adminId)
        .get();

    // IDs الموجودة بالفعل في Firestore
    final firebaseIds = firebaseSnapshot.docs.map((doc) => doc.id).toSet();

    for (final sub in localSubscribers) {
      if (!firebaseIds.contains(sub.id)) {
        SubscriberModel newSub = sub.copyWith(adminId: adminId);
        await _firestore
            .collection('subscribers')
            .doc(newSub.id)
            .set(newSub.toMap());
      }
    }
  }

  Future<void> savePackages(List<PackageModel> packages) async {
    if (_useSQLite) {
      // Delete all existing packages and insert new ones
      final db = await _sqliteService.database;
      await db.delete('packages');

      for (var package in packages) {
        await _sqliteService.insertPackage(package.toJson());
      }
    } else {
      final prefs = await SharedPreferences.getInstance();
      final packagesJson = jsonEncode(
        packages.map((pkg) => pkg.toJson()).toList(),
      );
      await prefs.setString(_packagesKey, packagesJson);
    }
  }

  Future<void> addPackage(PackageModel package) async {
    // if (_useSQLite) {
    //   await _sqliteService.insertPackage(package.toJson());
    // } else {
    //   final packages = await getPackages();
    //   packages.add(package);
    //   await savePackages(packages);
    // }
    PackageModel newPack = package.copyWith(adminId: adminId);
    await _firestore
        .collection('packages')
        .doc(newPack.id)
        .set(newPack.toMap());
    TelegramService()
        .sendPackageNotification(
          action: 'إضافة',
          packageName: package.name,
          price: package.price,
          speed: package.speed,
        )
        .catchError((error) {
          // Silently handle telegram errors to prevent app freeze
          print('Telegram notification error: $error');
        });
  }

  // Future<void> updatePackage(PackageModel package) async {
  //   if (_useSQLite) {
  //     await _sqliteService.updatePackage(package.toJson());
  //   } else {
  //     final packages = await getPackages();
  //     final index = packages.indexWhere((p) => p.id == package.id);
  //     if (index != -1) {
  //       packages[index] = package;
  //       await savePackages(packages);
  //     }
  //   }
  //   TelegramService()
  //       .sendPackageNotification(
  //         action: 'تعديل',
  //         packageName: package.name,
  //         price: package.price,
  //         speed: package.speed,
  //       )
  //       .catchError((error) {
  //         // Silently handle telegram errors to prevent app freeze
  //         print('Telegram notification error: $error');
  //       });
  // }

  Future<void> deletePackage(String packageId) async {
    try {
      await FirebaseFirestore.instance
          .collection('packages')
          .doc(packageId)
          .delete();
    } catch (e) {
      print('Error deleting package: $e');
    }
  }

  Future<PackageModel?> getPackageById(String id) async {
    final packages = await getPackagesFire();
    try {
      return packages.firstWhere(
        (p) => p.id.trim().toLowerCase() == id.trim().toLowerCase(),
      );
    } catch (e) {
      print('تحذير: لم يتم العثور على باقة حقيقية للمعرف: "${id.trim()}"');
      print('الباقات المتوفرة: ${packages.map((p) => p.id).toList()}');
      return null;
    }
  }

  // Subscriber operations
  Future<List<SubscriberModel>> getSubscribers() async {
    if (_useSQLite) {
      final subscriberMaps = await _sqliteService.getSubscribers();
      return subscriberMaps
          .map((map) => SubscriberModel.fromJson(map))
          .toList();
    } else {
      final prefs = await SharedPreferences.getInstance();
      final subscribersJson = prefs.getString(_subscribersKey);
      if (subscribersJson == null) return [];

      final List<dynamic> subscribersList = jsonDecode(subscribersJson);
      return subscribersList
          .map((json) => SubscriberModel.fromJson(json))
          .toList();
    }
  }

  Future<List<SubscriberModel>> getSubscribersFire() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('subscribers')
          .where("adminId", isEqualTo: adminId)
          .get();
      print("admin $adminId");
      return snapshot.docs.map((doc) {
        return SubscriberModel.fromMap(doc.data());
      }).toList();
    } catch (e) {
      print('Error fetching subscribers from Firestore: $e');
      return [];
    }
  }

  Future<void> saveSubscribers(List<SubscriberModel> subscribers) async {
    if (_useSQLite) {
      // Delete all existing subscribers and insert new ones
      final db = await _sqliteService.database;
      await db.delete('subscribers');

      for (var subscriber in subscribers) {
        await _sqliteService.insertSubscriber(subscriber.toJson());
      }
    } else {
      final prefs = await SharedPreferences.getInstance();
      final subscribersJson = jsonEncode(
        subscribers.map((sub) => sub.toJson()).toList(),
      );
      await prefs.setString(_subscribersKey, subscribersJson);
    }
  }

  Future<void> addSubscriber(SubscriberModel subscriber, {bool isSyncUpdate = false}) async {
    // final doc = await FirebaseFirestore.instance
    // .collection('users')
    // .doc(userId)
    // .get();
    final SubscriberModel newSub = subscriber.copyWith(adminId: adminId);
    await _firestore
        .collection('subscribers')
        .doc(newSub.id)
        .set(newSub.toMap());
    
    // Only create and activate user in SAS if this is not a sync update
    if (!isSyncUpdate) {
      await _sasApiService.login();
      final List<UserModel> admins = await _sasApiService.getManagers();

      PackageModel? package;
      if (subscriber.packageId.isNotEmpty) {
        package = await getPackageById(subscriber.packageId);
      }

      await _sasApiService.createUser(
        confirmPassword: newSub.password,
        parentId: admins.first.id,
        password: newSub.password,
        profileId: int.parse(package!.sasProfileId!),
        username: newSub.username,
      );
      try {
        await _sasApiService.activateUserInSas(
          username: newSub.username,
          newProfileId: int.parse(package.sasProfileId!),
        );
      } catch (e) {
        print('SAS غير متوفر أو فشل التفعيل: $e');
        // تجاهل الخطأ ولا توقف العملية
      }
      TelegramService()
          .sendPackageNotification(
            action: 'إضافة',
            packageName: package?.name ?? 'غير محدد',
            price: package?.price,
            speed: package?.speed,
          )
          .catchError((error) {
            // Silently handle telegram errors to prevent app freeze
            print('Telegram notification error: $error');
          });
    } else {
      print('==== [SYNC] Skipping SAS user creation and activation for ${subscriber.fullName} ====');
    }
  }

  Future<void> updateSubscriber(SubscriberModel subscriber, {bool isSyncUpdate = false}) async {
    SubscriberModel? oldSubscriber;
    final oldSubscriberMap = await _sqliteService.getSubscriberById(
      subscriber.id,
    );
    oldSubscriber = oldSubscriberMap != null
        ? SubscriberModel.fromJson(oldSubscriberMap)
        : null;

    String specificAction = 'تعديل'; // Default to generic modification
    try {
      await _firestore
          .collection('subscribers')
          .doc(subscriber.id)
          .update(subscriber.toMap());
      
      // Only update SAS if this is not a sync update
      if (!isSyncUpdate) {
        try {
          await _sasApiService.login();
          if (oldSubscriber != null) {
            await _sasApiService.updateUser(oldSubscriber.username, subscriber.username);
          }
        } catch (e) {
          print('SAS غير متوفر أو فشل التحديث: $e');
          // تجاهل الخطأ ولا توقف العملية
        }
      }
    } catch (e) {
      print(subscriber.id);
      print('Error updating subscriber: $e');
      rethrow;
    }

    // Only send notifications if this is not a sync update
    if (!isSyncUpdate) {
      if (oldSubscriber != null) {
        if (oldSubscriber.packageId != subscriber.packageId) {
          specificAction = 'تغيير باقة';
        } else if (oldSubscriber.isActive != subscriber.isActive) {
          specificAction = subscriber.isActive ? 'تفعيل' : 'إلغاء تفعيل';
        } else if (oldSubscriber.phoneNumber != subscriber.phoneNumber) {
          specificAction = 'تغيير رقم هاتف';
        } else if (oldSubscriber.fullName != subscriber.fullName) {
          specificAction = 'تغيير اسم المشترك';
        } else if (oldSubscriber.address != subscriber.address) {
          specificAction = 'تغيير عنوان المشترك';
        } else if (oldSubscriber.technicalNotes != subscriber.technicalNotes) {
          specificAction = 'تعديل الملاحظات الفنية للمشترك';
        }
        // Add more specific checks for other fields as needed
      }
      PackageModel? package;
      if (subscriber.packageId.isNotEmpty) {
        package = await getPackageById(subscriber.packageId);
      }

      TelegramService()
          .sendSubscriberNotification(
            action: specificAction,
            subscriberName: subscriber.fullName,
            subscriberPhone: subscriber.phoneNumber,
            packageName: package?.name,
            amount: package?.price,
          )
          .catchError((error) {
            // Silently handle telegram errors to prevent app freeze
            print('Telegram notification error: $error');
          });
    } else {
      print('==== [SYNC] Skipping notifications for sync update of ${subscriber.fullName} ====');
    }
  }

  Future<void> deleteSubscriber(String subscriberId) async {
    try {
      await FirebaseFirestore.instance
          .collection('subscribers')
          .doc(subscriberId)
          .delete();
    } catch (e) {
      print('Error deleting package: $e');
    }
  }

  // Activity log operations
  Future<List<ActivityLogModel>> getActivityLogs() async {
    if (_useSQLite) {
      final logMaps = await _sqliteService.getActivityLogs();
      return logMaps.map((map) => ActivityLogModel.fromJson(map)).toList();
    } else {
      final prefs = await SharedPreferences.getInstance();
      final logsJson = prefs.getString(_activityLogsKey);
      if (logsJson == null) return [];

      final List<dynamic> logsList = jsonDecode(logsJson);
      return logsList.map((json) => ActivityLogModel.fromJson(json)).toList();
    }
  }

  Future<List<ActivityLogModel>> getActivityLogsFire() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('activityLog')
          .where('adminId', isEqualTo: adminId)
          .get();

      return snapshot.docs.map((doc) {
        return ActivityLogModel.fromMap(doc.data());
      }).toList();
    } catch (e) {
      print('Error fetching packages from Firestore: $e');
      return [];
    }
  }

  Future<void> addActivityLog(ActivityLogModel log) async {
    await _firestore.collection('activityLog').doc(log.id).set(log.toMap());
  }

  // Payment record operations
  Future<List<PaymentRecordModel>> getPaymentRecords() async {
    if (_useSQLite) {
      final recordMaps = await _sqliteService.getPaymentRecords();
      return recordMaps.map((map) => PaymentRecordModel.fromJson(map)).toList();
    } else {
      final prefs = await SharedPreferences.getInstance();
      final recordsJson = prefs.getString(_paymentRecordsKey);
      if (recordsJson == null) return [];

      final List<dynamic> recordsList = jsonDecode(recordsJson);
      return recordsList
          .map((json) => PaymentRecordModel.fromJson(json))
          .toList();
    }
  }

  Future<List<PaymentRecordModel>> getPaymentRecordsFire() async {
    final snapshot = await FirebaseFirestore.instance
        .collection('paymentRecord')
        .where("adminId", isEqualTo: adminId)
        .get();

    return snapshot.docs.map((doc) {
      return PaymentRecordModel.fromMap(doc.data());
    }).toList();
  }

  Future<void> addPaymentRecord(PaymentRecordModel record) async {
    PaymentRecordModel newRecord = record.copyWith(adminId: adminId);
    await _firestore
        .collection('paymentRecord')
        .doc(newRecord.id)
        .set(newRecord.toMap());
    // if (_useSQLite) {
    //   await _sqliteService.insertPaymentRecord(record.toJson());
    // } else {
    //   final records = await getPaymentRecordsFire();
    //   records.add(record);
    //   final prefs = await SharedPreferences.getInstance();
    //   final recordsJson = jsonEncode(records.map((r) => r.toJson()).toList());
    //   await prefs.setString(_paymentRecordsKey, recordsJson);
    // }
    final subscriber = await getSubscriberById(record.subscriberId);
    if (subscriber != null) {
      // Check if this payment record is part of a renewal or debt adjustment
      // If so, we assume sendSubscriberNotification is handled by the caller
      // and we should not send a duplicate sendPaymentNotification.
      final isRenewalOrDebtAdjustment =
          ((record.notes?.contains('تجديد') ?? false) ||
          (record.notes?.contains('دين سابق') ?? false) ||
          (record.notes?.contains('سداد دين') ?? false));
      if (!isRenewalOrDebtAdjustment) {
        TelegramService()
            .sendPaymentNotification(
              subscriberName: subscriber.fullName,
              subscriberPhone: subscriber.phoneNumber,
              amount: record.amount,
              paymentMethod: record.paymentMethod,
              notes: record.notes,
            )
            .catchError((error) {
              // Silently handle telegram errors to prevent app freeze
              print('Telegram notification error: $error');
            });
      }
    }
  }

  // Backup and restore
  // Create a JSON backup
  Future<Map<String, dynamic>> createJsonBackup() async {
    final users = await getUsers();
    final packages = await getPackages();
    final subscribers = await getSubscribers();
    final activityLogs = await getActivityLogs();
    final paymentRecords = await getPaymentRecords();
    final expenseCategories = await getExpenseCategories(); // New
    final expenses = await getExpenses(); // New

    return {
      'users': users.map((u) => u.toJson()).toList(),
      'packages': packages.map((p) => p.toJson()).toList(),
      'subscribers': subscribers.map((s) => s.toJson()).toList(),
      'activityLog': activityLogs.map((l) => l.toJson()).toList(),
      'paymentRecords': paymentRecords.map((r) => r.toJson()).toList(),
      'expenseCategories': expenseCategories
          .map((c) => c.toJson())
          .toList(), // New
      'expenses': expenses.map((e) => e.toJson()).toList(), // New
      'backupDate': DateTime.now().toIso8601String(),
    };
  }

  // Save JSON backup to a file
  Future<String> saveJsonBackupToFile(String destinationPath) async {
    final backupData = await createJsonBackup();
    final backupJson = jsonEncode(backupData);

    final backupFileName =
        'isp_manager_backup_${DateTime.now().millisecondsSinceEpoch}.json';
    final backupFilePath = path_lib.join(destinationPath, backupFileName);

    final file = File(backupFilePath);
    await file.writeAsString(backupJson, flush: true);

    return backupFilePath;
  }

  // Share JSON backup
  Future<void> shareDatabaseAsJson() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final backupFilePath = await saveJsonBackupToFile(tempDir.path);

      await Share.shareXFiles([
        XFile(backupFilePath),
      ], text: 'نسخة احتياطية من بيانات تطبيق إدارة المشتركين');
    } catch (e) {
      print('Error sharing database as JSON: $e');
      // Optionally show a user-friendly message
    }
  }

  // Create a SQLite database backup
  Future<String> createSqliteBackup(String destinationPath) async {
    if (_useSQLite) {
      return await _sqliteService.createDatabaseBackup(destinationPath);
    } else {
      // If not using SQLite, migrate to SQLite first, then create backup
      await migrateToSQLite();
      return await _sqliteService.createDatabaseBackup(destinationPath);
    }
  }

  // Restore from JSON backup data
  Future<void> restoreFromJsonBackup(Map<String, dynamic> backup) async {
    try {
      final users = (backup['users'] as List)
          .map((json) => UserModel.fromJson(json))
          .toList();
      final packages = (backup['packages'] as List)
          .map((json) => PackageModel.fromJson(json))
          .toList();
      final subscribers = (backup['subscribers'] as List)
          .map((json) => SubscriberModel.fromJson(json))
          .toList();

      // Clear existing data
      if (_useSQLite) {
        await _sqliteService.clearAllTables();
      }

      await saveUsers(users);
      await savePackages(packages);
      await saveSubscribers(subscribers);

      if (backup.containsKey('activityLog')) {
        final logs = (backup['activityLog'] as List)
            .map((json) => ActivityLogModel.fromJson(json))
            .toList();

        if (_useSQLite) {
          for (var log in logs) {
            await _sqliteService.insertActivityLog(log.toJson());
          }
        } else {
          final prefs = await SharedPreferences.getInstance();
          final logsJson = jsonEncode(logs.map((l) => l.toJson()).toList());
          await prefs.setString(_activityLogsKey, logsJson);
        }
      }

      if (backup.containsKey('paymentRecords')) {
        final records = (backup['paymentRecords'] as List)
            .map((json) => PaymentRecordModel.fromJson(json))
            .toList();

        if (_useSQLite) {
          for (var record in records) {
            await _sqliteService.insertPaymentRecord(record.toJson());
          }
        } else {
          final prefs = await SharedPreferences.getInstance();
          final recordsJson = jsonEncode(
            records.map((r) => r.toJson()).toList(),
          );
          await prefs.setString(_paymentRecordsKey, recordsJson);
        }
      }

      // Restore expense categories
      if (backup.containsKey('expenseCategories')) {
        final categories = (backup['expenseCategories'] as List)
            .map((json) => ExpenseCategoryModel.fromJson(json))
            .toList();
        await saveExpenseCategories(categories);
      }

      // Restore expenses
      if (backup.containsKey('expenses')) {
        final expenses = (backup['expenses'] as List)
            .map((json) => ExpenseModel.fromJson(json))
            .toList();
        await saveExpenses(expenses);
      }
    } catch (e) {
      throw Exception('فشل في استعادة البيانات: ${e.toString()}');
    }
  }

  // Restore from JSON backup file
  Future<void> restoreFromJsonFile(String filePath) async {
    try {
      final file = File(filePath);

      if (!await file.exists()) {
        throw Exception('ملف النسخة الاحتياطية غير موجود');
      }

      final fileSize = await file.length();
      if (fileSize == 0) {
        throw Exception('ملف النسخة الاحتياطية فارغ');
      }

      final jsonString = await file.readAsString();

      if (jsonString.trim().isEmpty) {
        throw Exception('ملف النسخة الاحتياطية فارغ أو لا يحتوي على بيانات');
      }

      final backupData = jsonDecode(jsonString) as Map<String, dynamic>;

      // Validate that the backup contains the expected structure
      if (!backupData.containsKey('users') ||
          !backupData.containsKey('packages') ||
          !backupData.containsKey('subscribers')) {
        throw Exception(
          'ملف النسخة الاحتياطية لا يحتوي على البنية المطلوبة (users, packages, subscribers)',
        );
      }
      // Also check for new expense tables
      if (!backupData.containsKey('expenseCategories') ||
          !backupData.containsKey('expenses')) {
        // This is not a critical error, as older backups might not have these.
        // We can log a warning or handle it gracefully. For now, just proceed.
      }

      await restoreFromJsonBackup(backupData);
    } catch (e) {
      if (e.toString().contains('FormatException')) {
        throw Exception('ملف JSON غير صحيح أو تالف');
      }
      throw Exception('فشل في قراءة ملف النسخة الاحتياطية: ${e.toString()}');
    }
  }

  // Restore from SQLite database file
  Future<void> restoreFromSqliteFile(String filePath) async {
    try {
      // Ensure we're using SQLite
      _useSQLite = true;

      // Restore the database
      await _sqliteService.restoreFromDatabaseBackup(filePath);
    } catch (e) {
      throw Exception('فشل في استعادة قاعدة البيانات: ${e.toString()}');
    }
  }

  Future<void> resetDatabase() async {
    await _sqliteService.clearAllTables();

    // await initializeDatabase();
    TelegramService()
        .sendSystemNotification(
          title: 'إعادة ضبط التطبيق',
          description: 'تمت إعادة ضبط جميع البيانات في التطبيق بنجاح.',
        )
        .catchError((error) {
          // Silently handle telegram errors to prevent app freeze
          print('Telegram notification error: $error');
        });
  }

  Future<void> deleteDocsByAdminIdAcrossCollections() async {
    final firestore = FirebaseFirestore.instance;
    final collections = [
      'activityLog',
      'expenseCategory',
      'expenses',
      "message_templates",
      "mikrotik_devices",
      "network_devices",
      "packages",
      "paymentRecord",
      "sas_servers",
      "subscribers",
    ];

    for (final collectionName in collections) {
      while (true) {
        final snapshot = await firestore
            .collection(collectionName)
            .where('adminId', isEqualTo: adminId)
            .get();

        if (snapshot.docs.isEmpty) break;

        final batch = firestore.batch();
        for (final doc in snapshot.docs) {
          batch.delete(doc.reference);
        }
        await batch.commit();

        print('Deleted ${snapshot.docs.length} docs from $collectionName');
      }
    }
  }

  // Method to migrate from SharedPreferences to SQLite
  Future<void> migrateToSQLite() async {
    if (!_useSQLite) {
      _useSQLite = true;

      // Get all data from SharedPreferences
      final prefs = await SharedPreferences.getInstance();

      // Check if there's data in SharedPreferences
      if (prefs.getString(_usersKey) != null) {
        // Get all data
        final users = await getUsers();
        final packages = await getPackagesFire();
        final subscribers = await getSubscribersFire();
        final activityLogs = await getActivityLogsFire();
        final paymentRecords = await getPaymentRecordsFire();
        final expenseCategories = await getExpenseCategories(); // New
        final expenses = await getExpensesFire(); // New

        // Clear SQLite tables
        await _sqliteService.clearAllTables();

        // Insert data into SQLite
        for (var user in users) {
          await _sqliteService.insertUser(user.toJson());
        }

        for (var package in packages) {
          await _sqliteService.insertPackage(package.toJson());
        }

        for (var subscriber in subscribers) {
          await _sqliteService.insertSubscriber(subscriber.toJson());
        }

        for (var log in activityLogs) {
          await _sqliteService.insertActivityLog(log.toJson());
        }

        for (var record in paymentRecords) {
          await _sqliteService.insertPaymentRecord(record.toJson());
        }

        for (var category in expenseCategories) {
          // New
          await _sqliteService.insertExpenseCategory(category.toJson());
        }

        for (var expense in expenses) {
          // New
          await _sqliteService.insertExpense(expense.toJson());
        }
      }
    }
  }

  // Message template operations
  Future<List<MessageTemplateModel>> getMessageTemplates() async {
    if (_useSQLite) {
      final templateMaps = await _sqliteService.getMessageTemplates();
      return templateMaps
          .map((map) => MessageTemplateModel.fromJson(map))
          .toList();
    } else {
      final prefs = await SharedPreferences.getInstance();
      final templatesJson = prefs.getString(_messageTemplatesKey);
      if (templatesJson == null) return [];

      final List<dynamic> templatesList = jsonDecode(templatesJson);
      return templatesList
          .map((json) => MessageTemplateModel.fromJson(json))
          .toList();
    }
  }

  Future<List<MessageTemplateModel>> getMessageTemplatesFire() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('message_templates')
          .where('adminId', isEqualTo: adminId)
          .get();

      return snapshot.docs.map((doc) {
        return MessageTemplateModel.fromMap(doc.data());
      }).toList();
    } catch (e) {
      print('Error fetching Messages from Firestore: $e');
      return [];
    }
  }

  Future<void> saveMessageTemplates(
    List<MessageTemplateModel> templates,
  ) async {
    if (_useSQLite) {
      // Delete all existing templates and insert new ones
      final db = await _sqliteService.database;
      await db.delete('message_templates');

      for (var template in templates) {
        await _sqliteService.insertMessageTemplate(template.toJson());
      }
    } else {
      final prefs = await SharedPreferences.getInstance();
      final templatesJson = jsonEncode(
        templates.map((t) => t.toJson()).toList(),
      );
      await prefs.setString(_messageTemplatesKey, templatesJson);
    }
  }

  Future<void> addMessageTemplate(MessageTemplateModel template) async {
    MessageTemplateModel newMess = template.copyWith(adminId: adminId);
    await _firestore
        .collection('message_templates')
        .doc(newMess.id)
        .set(newMess.toMap());
  }

  Future<void> updateMessageTemplate(MessageTemplateModel template) async {
    try {
      await FirebaseFirestore.instance
          .collection('message_templates')
          .doc(template.id) // لازم الـ id يكون مضبوط
          .update(template.toMap());
    } catch (e) {
      print('Error updating package: $e');
    }
  }

  Future<void> deleteMessageTemplate(String templateId) async {
    if (_useSQLite) {
      await FirebaseFirestore.instance
          .collection('message_templates')
          .doc(templateId)
          .delete();
    } else {
      final templates = await getMessageTemplates();
      templates.removeWhere((t) => t.id == templateId);
      await saveMessageTemplates(templates);
    }
  }

  Future<List<MessageTemplateModel>> getMessageTemplatesByType(
    MessageTemplateType type,
  ) async {
    final templates = await getMessageTemplatesFire();
    return templates.where((t) => t.type == type).toList();
  }

  Future<MessageTemplateModel?> getDefaultMessageTemplate(
    MessageTemplateType type,
  ) async {
    final templates = await getMessageTemplatesByType(type);
    try {
      return templates.firstWhere((t) => t.isDefault);
    } catch (e) {
      return templates.isNotEmpty ? templates.first : null;
    }
  }

  // Expense Category operations
  Future<List<ExpenseCategoryModel>> getExpenseCategories() async {
    if (_useSQLite) {
      final categoryMaps = await _sqliteService.getExpenseCategories();
      return categoryMaps
          .map((map) => ExpenseCategoryModel.fromJson(map))
          .toList();
    } else {
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = prefs.getString(_expenseCategoriesKey);
      if (categoriesJson == null) return [];

      final List<dynamic> categoriesList = jsonDecode(categoriesJson);
      return categoriesList
          .map((json) => ExpenseCategoryModel.fromJson(json))
          .toList();
    }
  }

  Future<List<ExpenseCategoryModel>> getExpenseCategoriesFire() async {
    final snapshot = await FirebaseFirestore.instance
        .collection('expenseCategory')
        .get();

    return snapshot.docs.map((doc) {
      return ExpenseCategoryModel.fromMap(doc.data());
    }).toList();
  }

  Future<void> saveExpenseCategories(
    List<ExpenseCategoryModel> categories,
  ) async {
    if (_useSQLite) {
      final db = await _sqliteService.database;
      await db.delete('expense_categories');
      for (var category in categories) {
        await _sqliteService.insertExpenseCategory(category.toJson());
      }
    } else {
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = jsonEncode(
        categories.map((cat) => cat.toJson()).toList(),
      );
      await prefs.setString(_expenseCategoriesKey, categoriesJson);
    }
  }

  Future<void> addExpenseCategory(ExpenseCategoryModel category) async {
    try {
      // if (_useSQLite) {
      //   await _sqliteService.insertExpenseCategory(category.toJson());
      // } else {
      //   final categories = await getExpenseCategories();
      //   categories.add(category);
      //   await saveExpenseCategories(categories);
      // }
      await _firestore
          .collection('expenseCategory')
          .doc(category.id)
          .set(category.toMap());
      // Send Telegram notification
      TelegramService()
          .sendExpenseNotification(
            action: 'إضافة فئة',
            description: 'فئة مصروف جديدة: ${category.name}',
            categoryName: category.name,
            notes: category
                .name, // Changed from category.description to category.name
          )
          .catchError((error) {
            // Silently handle telegram errors to prevent app freeze
            print('Telegram notification error: $error');
          });
    } catch (e) {
      // Re-throw database errors
      rethrow;
    }
  }

  Future<void> updateExpenseCategory(ExpenseCategoryModel category) async {
    try {
      await FirebaseFirestore.instance
          .collection('expenseCategory')
          .doc(category.id) // لازم الـ id يكون مضبوط
          .update(category.toMap());
      // if (_useSQLite) {
      //   await _sqliteService.updateExpenseCategory(category.toJson());
      // } else {
      //   final categories = await getExpenseCategories();
      //   final index = categories.indexWhere((c) => c.id == category.id);
      //   if (index != -1) {
      //     categories[index] = category;
      //     await saveExpenseCategories(categories);
      //   }
      // }

      // Send Telegram notification
      TelegramService()
          .sendExpenseNotification(
            action: 'تعديل فئة',
            description: 'تم تعديل فئة المصروف: ${category.name}',
            categoryName: category.name,
            notes: category
                .name, // Changed from category.description to category.name
          )
          .catchError((error) {
            // Silently handle telegram errors to prevent app freeze
            print('Telegram notification error: $error');
          });
    } catch (e) {
      // Re-throw database errors
      rethrow;
    }
  }

  Future<void> deleteExpenseCategory(String categoryId) async {
    try {
      // Get category details before deletion for notification
      final categories = await getExpenseCategoriesFire();
      final categoryToDelete = categories.firstWhere(
        (c) => c.id == categoryId,
        orElse: () => ExpenseCategoryModel(
          adminId: adminId,
          id: '',
          name: 'فئة محذوفة',
          createdAt: DateTime.now(),
        ),
      ); // Added createdAt

      await FirebaseFirestore.instance
          .collection('expenseCategory')
          .doc(categoryId)
          .delete();

      // Send Telegram notification
      TelegramService()
          .sendExpenseNotification(
            action: 'حذف فئة',
            description: 'تم حذف فئة المصروف: ${categoryToDelete.name}',
            categoryName: categoryToDelete.name,
            notes: categoryToDelete
                .name, // Changed from categoryToDelete.description to categoryToDelete.name
          )
          .catchError((error) {
            // Silently handle telegram errors to prevent app freeze
            print('Telegram notification error: $error');
          });
    } catch (e) {
      // Re-throw database errors
      rethrow;
    }
  }

  Future<ExpenseCategoryModel?> getExpenseCategoryById(String id) async {
    if (_useSQLite) {
      final categoryMap = await _sqliteService.getExpenseCategoryById(id);
      return categoryMap != null
          ? ExpenseCategoryModel.fromJson(categoryMap)
          : null;
    } else {
      final categories = await getExpenseCategories();
      try {
        return categories.firstWhere((c) => c.id == id);
      } catch (e) {
        return null;
      }
    }
  }

  // Expense operations
  Future<List<ExpenseModel>> getExpenses() async {
    if (_useSQLite) {
      final expenseMaps = await _sqliteService.getExpenses();
      return expenseMaps.map((map) => ExpenseModel.fromJson(map)).toList();
    } else {
      final prefs = await SharedPreferences.getInstance();
      final expensesJson = prefs.getString(_expensesKey);
      if (expensesJson == null) return [];

      final List<dynamic> expensesList = jsonDecode(expensesJson);
      return expensesList.map((json) => ExpenseModel.fromJson(json)).toList();
    }
  }

  Future<List<ExpenseModel>> getExpensesFire() async {
    final snapshot = await FirebaseFirestore.instance
        .collection('expenses')
        .where("adminId", isEqualTo: adminId)
        .get();

    return snapshot.docs.map((doc) {
      return ExpenseModel.fromMap(doc.data());
    }).toList();
  }

  Future<void> saveExpenses(List<ExpenseModel> expenses) async {
    if (_useSQLite) {
      final db = await _sqliteService.database;
      await db.delete('expenses');
      for (var expense in expenses) {
        await _sqliteService.insertExpense(expense.toJson());
      }
    } else {
      final prefs = await SharedPreferences.getInstance();
      final expensesJson = jsonEncode(
        expenses.map((exp) => exp.toJson()).toList(),
      );
      await prefs.setString(_expensesKey, expensesJson);
    }
  }

  Future<void> addExpense(ExpenseModel expense) async {
    try {
      await _firestore
          .collection('expenses')
          .doc(expense.id)
          .set(expense.toMap());
      // if (_useSQLite) {
      //   await _sqliteService.insertExpense(expense.toJson());
      // } else {
      //   final expenses = await getExpenses();
      //   expenses.add(expense);
      //   await saveExpenses(expenses);
      // }
      // Send Telegram notification
      final categories = await getExpenseCategories();
      final category = categories.firstWhere(
        (cat) => cat.id == expense.categoryId,
        orElse: () => ExpenseCategoryModel(
          adminId: adminId,
          id: '',
          name: 'غير محدد',
          createdAt: DateTime.now(),
        ),
      );

      TelegramService()
          .sendExpenseNotification(
            action: 'إضافة مصروف',
            description: expense
                .notes, // Changed from expense.description to expense.notes
            categoryName: category.name,
            amount: expense.amount,
            notes: expense.notes,
          )
          .catchError((error) {
            // Silently handle telegram errors to prevent app freeze
            print('Telegram notification error: $error');
          });
    } catch (e) {
      // Re-throw database errors
      rethrow;
    }
  }

  Future<double> getTotalExpensesAmount() async {
    final expenses = await getExpensesFire();
    return expenses.fold<double>(
      0.0,
      (double sum, ExpenseModel expense) => sum + expense.amount,
    );
  }

  Future<double> getTotalCollectedAmount() async {
    // if (_useSQLite) {
    //     final db = await _sqliteService.database;
    //     final result = await db.rawQuery('''
    //       SELECT SUM(amount) as total
    //       FROM payment_records
    //     ''');
    //     print(result.first['total']);
    //     return (result.first['total'] as double?) ?? 0.0;
    //   } else {}
    final paymentRecords = await getPaymentRecordsFire();

    return paymentRecords.fold<double>(0.0, (
      double sum,
      PaymentRecordModel record,
    ) {
      return sum + record.amount;
    });
  }

  Future<double> getCurrentBalance() async {
    final totalCollected = await getTotalCollectedAmount();
    final totalExpenses = await getTotalExpensesAmount();
    return totalCollected - totalExpenses;
  }

  String generateId() => _uuid.v4();

  /// Synchronizes packages and subscribers from SAS API to local database.
  /// Returns true if synchronization was successful, false otherwise.
  Future<bool> syncFromSas() async {
    print('==== [SYNC] syncFromSas CALLED ====');
    try {
      // 1. Attempt to login to SAS API
      bool isLoggedIn = false;
      try {
        isLoggedIn = await _sasApiService.login();
      } catch (e) {
        print('SAS غير متوفر أو فشل تسجيل الدخول: $e');
      }
      if (!isLoggedIn) {
        print('SAS API Login failed or not configured. Skipping SAS sync.');
        return true; // اعتبر المزامنة ناجحة إذا لم يوجد SAS
      }
      print('SAS API Login successful. Starting synchronization...');

      // 2. Fetch and synchronize packages (profiles) from SAS API with better error handling
      print('==== [SYNC] Fetching packages from SAS ====');
      final sasPackages = await _sasApiService.getProfiles();
      print('==== [SYNC] Found ${sasPackages.length} packages from SAS ====');
      
      if (sasPackages.isNotEmpty) {
        final currentLocalPackages = await getPackagesFire();
        final List<PackageModel> updatedLocalPackages = [];
        
        for (var sasPackage in sasPackages) {
          // Try to find existing package by sasProfileId or name
          final existingPackage = currentLocalPackages.firstWhere(
            (p) =>
                p.sasProfileId == sasPackage.sasProfileId ||
                p.name == sasPackage.name,
            orElse: () => PackageModel(
              adminId: DatabaseService().adminId,
              // Create a dummy if not found
              id: '',
              serverId: "",
              name: '',
              price: 0,
              durationInDays: 0,
              speed: '',
              deviceCount: 0,
              createdAt: DateTime.now(),
            ),
          );

          print(
            'Processing SAS package: ${sasPackage.name} (ID: ${sasPackage.sasProfileId})',
          );

          if (existingPackage.id.isNotEmpty) {
            print('Updating existing package: ${existingPackage.name}');
            // Update existing package
            updatedLocalPackages.add(
              existingPackage.copyWith(
                adminId: DatabaseService().adminId,
                name: sasPackage.name,
                price: sasPackage.price,
                sellingPrice:
                    existingPackage.sellingPrice, // Preserve selling price
                durationInDays: sasPackage.durationInDays,
                speed: sasPackage.speed,
                deviceCount: sasPackage.deviceCount,
                notes: sasPackage.notes,
                isActive: sasPackage.isActive,
                sasProfileId:
                    sasPackage.sasProfileId, // Ensure SAS ID is linked
              ),
            );
          } else {
            print('Adding new package: ${sasPackage.name}');
            // Add new package
            updatedLocalPackages.add(
              PackageModel(
                adminId: DatabaseService().adminId,
                id: _firestore.collection('packages').doc().id,
                serverId: sasPackage.id,
                name: sasPackage.name,
                price: sasPackage.price,
                durationInDays: sasPackage.durationInDays,
                speed: sasPackage.speed,
                deviceCount: sasPackage.deviceCount,
                notes: sasPackage.notes,
                createdAt: DateTime.now(), // Set current time for new entry
                isActive: sasPackage.isActive,
                sasProfileId: sasPackage.sasProfileId, // Link to SAS Profile ID
              ),
            );
          }
        }
        
        // Save all updated/new packages to local database
        print('==== [SYNC] Saving ${updatedLocalPackages.length} packages ====');
        for (var package in updatedLocalPackages) {
          if (package.id.isNotEmpty &&
              currentLocalPackages.any((p) => p.id == package.id)) {
            await DatabaseService().updatePackage(package);
          } else {
            await DatabaseService().addPackage(package);
          }
        }
        print('Packages synchronized successfully.');
      } else {
        print('No packages found on SAS API or failed to fetch.');
      }

      // 3. Fetch and synchronize subscribers (users) from SAS API with improved pagination
      print('==== [SYNC] Fetching subscribers from SAS ====');
      List<SasUser> sasUsers = [];
      int currentPage = 1;
      int perPage = 500; // Increased back to 500 as requested
      int? totalUsers;
      int maxPages = 50; // Add safety limit to prevent infinite loops
      int consecutiveEmptyPages = 0; // Track empty pages
      int consecutiveErrors = 0; // Track consecutive errors
      bool shouldContinue = true; // Flag to control the loop

      while (shouldContinue) {
        print('==== [SYNC] Fetching SAS users - Page $currentPage (per page: $perPage) ====');
        
        try {
          final sasUsersResponse = await _sasApiService.getUsers(
            page: currentPage,
            perPage: perPage,
          );
          
          if (sasUsersResponse != null && sasUsersResponse.containsKey('data')) {
            final List<dynamic> usersData = sasUsersResponse['data'];
            final List<SasUser> currentBatch = usersData
                .map((json) => SasUser.fromJson(json))
                .toList();
            
            print('==== [SYNC] Fetched ${currentBatch.length} users from page $currentPage ====');
            
            // Reset error counter on successful request
            consecutiveErrors = 0;
            
            if (currentBatch.isNotEmpty) {
              sasUsers.addAll(currentBatch);
              consecutiveEmptyPages = 0; // Reset empty page counter
            } else {
              consecutiveEmptyPages++;
              print('==== [SYNC] Empty page detected. Consecutive empty pages: $consecutiveEmptyPages ====');
            }

            // Attempt to get total users from the response, if available
            if (sasUsersResponse.containsKey('total') &&
                sasUsersResponse['total'] is int) {
              totalUsers = sasUsersResponse['total'];
              print('==== [SYNC] Total users reported by API: $totalUsers ====');
            } else if (sasUsersResponse.containsKey('meta') &&
                sasUsersResponse['meta']['total'] is int) {
              totalUsers = sasUsersResponse['meta']['total'];
              print('==== [SYNC] Total users from meta: $totalUsers ====');
            }

            // Stop conditions
            if (currentBatch.isEmpty && currentPage > 1) {
              print('==== [SYNC] Empty page detected, stopping pagination ====');
              shouldContinue = false;
            } else if (totalUsers != null && sasUsers.length >= totalUsers) {
              print('==== [SYNC] Reached reported total users: $totalUsers ====');
              shouldContinue = false;
            } else if (consecutiveEmptyPages >= 3) {
              print('==== [SYNC] Stopping due to consecutive empty pages ====');
              shouldContinue = false;
            } else if (currentPage >= maxPages) {
              print('==== [SYNC] Reached maximum page limit ($maxPages), stopping ====');
              shouldContinue = false;
            } else {
              currentPage++;
            }
          } else {
            print('==== [SYNC] Invalid response format or no data key, stopping ====');
            shouldContinue = false;
          }
        } catch (e) {
          consecutiveErrors++;
          print('==== [SYNC] Error fetching page $currentPage: $e ====');
          print('==== [SYNC] Consecutive errors: $consecutiveErrors ====');
          
          // Stop if we've had too many consecutive errors
          if (consecutiveErrors >= 3) {
            print('==== [SYNC] Too many consecutive errors, stopping pagination ====');
            shouldContinue = false;
          } else if (currentPage > 10) {
            print('==== [SYNC] Too many errors after page 10, stopping pagination ====');
            shouldContinue = false;
          } else {
            currentPage++;
          }
        }
        
        // Add small delay between requests to prevent overwhelming the server
        if (shouldContinue) {
          await Future.delayed(Duration(milliseconds: 100));
        }
      }

      print('==== [SYNC] Total SAS users fetched: ${sasUsers.length} ====');
      
      if (sasUsers.isNotEmpty) {
        final currentLocalSubscribers = await getSubscribersFire();
        print(
          '==== [SYNC DEBUG] Current local subscribers count: ${currentLocalSubscribers.length} ====',
        );

        // Show all current subscribers with their debts before sync
        print('==== [SYNC DEBUG] Current subscribers before sync: ====');
        for (var sub in currentLocalSubscribers) {
          print(
            '  - ${sub.fullName}: Debt=${sub.debtAmount}, SAS_ID=${sub.sasServerId}, Username=${sub.username}',
          );
        }
        
        // Create a map for efficient lookup of existing subscribers
        final Map<String, SubscriberModel> localSubscribersMap = {};
        for (var sub in currentLocalSubscribers) {
          // Add multiple keys for each subscriber for better matching
          if (sub.sasServerId != null) {
            localSubscribersMap['sas_${sub.sasServerId}'] = sub;
          }
          if (sub.username.isNotEmpty) {
            localSubscribersMap['username_${sub.username}'] = sub;
          }
          if (sub.phoneNumber.isNotEmpty) {
            localSubscribersMap['phone_${sub.phoneNumber}'] = sub;
          }
        }
        print(
          '==== [SYNC DEBUG] Local subscribers map size: ${localSubscribersMap.length} ====',
        );

        final List<SubscriberModel> subscribersToSave = [];
        int processedCount = 0;
        
        for (var sasUser in sasUsers) {
          processedCount++;
          if (processedCount % 50 == 0) {
            print('==== [SYNC] Processed $processedCount/${sasUsers.length} users ====');
          }
          
          print(
            'Processing SAS user: ${sasUser.fullName} (Username: ${sasUser.username}, Profile ID: ${sasUser.profileId})',
          );

          SubscriberModel? existingSubscriber;
          String matchedBy = '';

          // Prioritize lookup by sasServerId first
          if (sasUser.id != null &&
              localSubscribersMap.containsKey('sas_${sasUser.id}')) {
            existingSubscriber = localSubscribersMap['sas_${sasUser.id}'];
            matchedBy = 'SAS ID: ${sasUser.id}';
            print(
              '==== [SYNC DEBUG] Found subscriber by SAS ID: ${sasUser.id} ====',
            );
          } else if (sasUser.username.isNotEmpty &&
              localSubscribersMap.containsKey('username_${sasUser.username}')) {
            existingSubscriber =
                localSubscribersMap['username_${sasUser.username}'];
            matchedBy = 'Username: ${sasUser.username}';
            print(
              '==== [SYNC DEBUG] Found subscriber by username: ${sasUser.username} ====',
            );
          } else if (sasUser.phone != null &&
              sasUser.phone!.isNotEmpty &&
              localSubscribersMap.containsKey('phone_${sasUser.phone}')) {
            existingSubscriber = localSubscribersMap['phone_${sasUser.phone}'];
            matchedBy = 'Phone: ${sasUser.phone}';
            print(
              '==== [SYNC DEBUG] Found subscriber by phone: ${sasUser.phone} ====',
            );
          } else {
            print(
              '==== [SYNC DEBUG] No existing subscriber found for SAS user: ${sasUser.fullName} (SAS ID: ${sasUser.id}, Username: ${sasUser.username}, Phone: ${sasUser.phone}) ====',
            );
          }

          // Find the corresponding local package ID using sasProfileId
          final matchingPackage = (await getPackagesFire()).firstWhere(
            (p) => p.sasProfileId == sasUser.profileId?.toString(),
            orElse: () => PackageModel(
              adminId: user!.uid,
              // Default package if not found
              id: 'unknown_package',
              serverId: "",
              name: 'غير معروف',
              price: 0,
              durationInDays: 30,
              speed: '',
              deviceCount: 0,
              createdAt: DateTime.now(),
            ),
          );

          print(
            'Found matching package: ${matchingPackage.name} (ID: ${matchingPackage.id}) for SAS user profile ID: ${sasUser.profileId}',
          );
          
          if (existingSubscriber != null) {
            print(
              '==== [SYNC DEBUG] Found existing subscriber: ${existingSubscriber.fullName} (ID: ${existingSubscriber.id}) with debt: ${existingSubscriber.debtAmount} ====',
            );
            print('==== [SYNC DEBUG] Matched by: $matchedBy ====');
            print(
              'Preserving debt amount: ${existingSubscriber.debtAmount} for ${existingSubscriber.fullName}',
            );
            
            // Update existing subscriber while preserving debt amount and other financial data
            final updatedSubscriber = existingSubscriber.copyWith(
              adminId: DatabaseService().adminId,
              fullName: sasUser.fullName,
              phoneNumber: sasUser.phone ?? existingSubscriber.phoneNumber,
              username: sasUser.username,
              password: existingSubscriber
                  .password, // Keep existing password as SAS API doesn't return it
              packageId: matchingPackage.id,
              packageName: matchingPackage.name,
              subscriptionEnd: sasUser.expiration,
              isActive: sasUser.isActive,
              sasServerId: sasUser.id, // Link to SAS User ID
              // Preserve existing financial data
              debtAmount:
                  existingSubscriber.debtAmount, // المحافظة على الدين المستحق
              paymentStatus:
                  existingSubscriber.paymentStatus, // المحافظة على حالة الدفع
              address: existingSubscriber.address, // المحافظة على العنوان
              macAddress:
                  existingSubscriber.macAddress, // المحافظة على عنوان MAC
              routerName:
                  existingSubscriber.routerName, // المحافظة على اسم الراوتر
              technicalNotes: existingSubscriber
                  .technicalNotes, // المحافظة على الملاحظات الفنية
              subscriptionStart: existingSubscriber
                  .subscriptionStart, // المحافظة على تاريخ بداية الاشتراك
              subscriptionType: existingSubscriber
                  .subscriptionType, // المحافظة على نوع الاشتراك
            );
            subscribersToSave.add(updatedSubscriber);
            print(
              '==== [SYNC DEBUG] Final debt amount for updated subscriber ${updatedSubscriber.fullName}: ${updatedSubscriber.debtAmount} ====',
            );
          } else {
            print(
              '==== [SYNC DEBUG] No existing subscriber found for SAS user: ${sasUser.fullName} (SAS ID: ${sasUser.id}) ====',
            );
            print('Adding new subscriber: ${sasUser.fullName}');
            final newSubscriber = SubscriberModel(
              adminId: DatabaseService().adminId,
              id: _uuid.v4(), // Generate new UUID for local storage
              fullName: sasUser.fullName,
              phoneNumber: sasUser.phone ?? '',
              packageId: matchingPackage.id,
              packageName: matchingPackage.name,
              address: 'غير محدد', // Default value, as not in SasUser
              paymentStatus: PaymentStatus.pending, // Default value
              subscriptionStart: DateTime.now(), // Default to now for new users
              subscriptionEnd: sasUser.expiration,
              macAddress: '', // Default
              routerName: '', // Default
              technicalNotes: 'تمت المزامنة من SAS Radius',
              debtAmount: 0.0, // Default
              createdAt: DateTime.now(),
              isActive: sasUser.isActive,
              subscriptionType: SubscriptionType.broadband, // Default
              username: sasUser.username,
              password: '', // No password from SAS API, user might set it later
              sasServerId: sasUser.id, // Link to SAS User ID
            );
            subscribersToSave.add(newSubscriber);
            print(
              '==== [SYNC DEBUG] Final debt amount for new subscriber ${newSubscriber.fullName}: ${newSubscriber.debtAmount} ====',
            );
          }
        }
        
        // Save all updated/new subscribers to local database with progress tracking
        print(
          '==== [SYNC] Saving ${subscribersToSave.length} subscribers to database ====',
        );
        
        int savedCount = 0;
        for (var subscriber in subscribersToSave) {
          savedCount++;
          if (savedCount % 50 == 0) {
            print('==== [SYNC] Saved $savedCount/${subscribersToSave.length} subscribers ====');
          }
          
          final existingInDb = await getSubscriberById(subscriber.id);
          if (existingInDb != null) {
            print(
              '==== [SYNC] Updating subscriber ${subscriber.fullName} with debt: ${subscriber.debtAmount} ====',
            );
            await DatabaseService().updateSubscriber(subscriber, isSyncUpdate: true);
          } else {
            print(
              '==== [SYNC] Inserting new subscriber ${subscriber.fullName} with debt: ${subscriber.debtAmount} ====',
            );
            await DatabaseService().addSubscriber(subscriber, isSyncUpdate: true);
          }
        }
        
        print(
          'Subscribers synchronized successfully with preserved debt amounts.',
        );

        // Debug: Show all subscribers after sync to verify debts are preserved
        print('==== [SYNC DEBUG] Verifying subscribers after sync: ====');
        final finalSubscribers = await getSubscribersFire();
        for (var sub in finalSubscribers) {
          print(
            '  - ${sub.fullName}: Debt=${sub.debtAmount}, SAS_ID=${sub.sasServerId}, Username=${sub.username}',
          );
        }

        final debtorsAfterSync = finalSubscribers
            .where((s) => s.debtAmount > 0)
            .toList();
        print(
          '==== [SYNC DEBUG] Subscribers with debts after sync: ${debtorsAfterSync.length} ====',
        );
      } else {
        print('No users found on SAS API or failed to fetch.');
      }
      
      print('==== [SYNC] Synchronization completed successfully ====');
      return true; // Synchronization successful
    } catch (e) {
      print('Error during SAS synchronization: $e');
      return false; // Synchronization failed
    }
  }

  // Financial reconciliation method
  Future<void> reconcileFinancialData({
    double? newTotalCollected,
    double? newTotalExpenses,
    String? notes,
  }) async {
    try {
      String reconciliationDetails = '';
      // مطابقة المقبوضات
      if (newTotalCollected != null) {
        // final db = await _sqliteService.database;
        // await db.delete('payment_records');
        final querySnapshot = await _firestore
            .collection("paymentRecord")
            .where("adminId", isEqualTo: adminId)
            .get();
        for (final doc in querySnapshot.docs) {
          await doc.reference.delete();
        }
        // إضافة سجل واحد جديد
        final reconciliationRecord = PaymentRecordModel(
          adminId: adminId,
          id: generateId(),
          subscriberId: 'reconciliation',
          amount: newTotalCollected,
          paymentMethod: 'مطابقة حسابات',
          paymentDate: DateTime.now(),
          recordedBy: 'النظام',
          notes:
              'مطابقة شاملة للمقبوضات${notes != null && notes.isNotEmpty ? ' - $notes' : ''}',
        );
        final reconciliationRecordExpenses = ExpenseModel(
          adminId: adminId,
          id: generateId(),
          categoryId: 'reconciliation',
          amount: newTotalExpenses ?? 0.0,

          timestamp: DateTime.now(),

          notes:
              'مطابقة شاملة للمقبوضات${notes != null && notes.isNotEmpty ? ' - $notes' : ''}',
        );
        await addPaymentRecord(reconciliationRecord);
        await addExpense(reconciliationRecordExpenses);
        final formattedAmount = await AppSettingsService.formatCurrency(
          newTotalCollected,
        );
        reconciliationDetails +=
            'تم تعيين إجمالي المقبوضات إلى $formattedAmount\n';
      }
      // مطابقة المصاريف
      if (newTotalExpenses != null) {
        final prefs = await SharedPreferences.getInstance();
        List<ExpenseModel> allExpenses = await getExpensesFire();
        allExpenses.clear();
        final reconciliationExpense = ExpenseModel(
          adminId: adminId,
          id: generateId(),
          categoryId: '',
          amount: newTotalExpenses,
          notes:
              'مطابقة شاملة للمصاريف${notes != null && notes.isNotEmpty ? ' - $notes' : ''}',
          timestamp: DateTime.now(),
        );
        allExpenses.add(reconciliationExpense);
        await prefs.setString(
          _expensesKey,
          jsonEncode(allExpenses.map((e) => e.toJson()).toList()),
        );

        final formattedAmount = await AppSettingsService.formatCurrency(
          newTotalExpenses,
        );
        reconciliationDetails +=
            'تم تعيين إجمالي المصاريف إلى $formattedAmount\n';
      }
      // إرسال إشعار تلغرام
      if (reconciliationDetails.isNotEmpty) {
        Future.microtask(
          () => TelegramService()
              .sendExpenseNotification(
                action: 'مطابقة حسابات',
                description: 'تمت مطابقة الحسابات المالية',
                notes: reconciliationDetails,
              )
              .catchError((error) {
                print('Telegram notification error: $error');
              }),
        );
      }
    } catch (e) {
      print('Error in reconcileFinancialData: $e');
      rethrow;
    }
  }

  // Method specifically for adding previous debt with proper notifications
  Future<void> updateSubscriberWithDebt(
    SubscriberModel updatedSubscriber,
    double debtAmount,
    String notes,
  ) async {
    // Update the subscriber in database
    await _firestore
        .collection('subscribers')
        .doc(updatedSubscriber.id)
        .update(updatedSubscriber.toMap());
    // Send specific Telegram notification for previous debt (non-blocking)
    TelegramService()
        .sendSubscriberNotification(
          action: 'إضافة دين سابق',
          subscriberName: updatedSubscriber.fullName,
          subscriberPhone: updatedSubscriber.phoneNumber,
          amount: debtAmount,
          additionalInfo: notes.isNotEmpty ? notes : null,
        )
        .catchError((error) {
          // Silently handle telegram errors to prevent app freeze
          print('Telegram notification error: $error');
        });
  }

  // Method specifically for payment recording with proper notifications
  Future<void> updateSubscriberWithPayment(
    SubscriberModel updatedSubscriber,
    double paymentAmount,
    String paymentMethod,
    String notes,
  ) async {
    // Update the subscriber in database
    await _firestore
        .collection('subscribers')
        .doc(updatedSubscriber.id)
        .update(updatedSubscriber.toMap());
    // Send specific Telegram notification for payment (non-blocking)
    TelegramService()
        .sendSubscriberNotification(
          action: 'تسجيل دفعة',
          subscriberName: updatedSubscriber.fullName,
          subscriberPhone: updatedSubscriber.phoneNumber,
          amount: paymentAmount,
          additionalInfo:
              'طريقة الدفع: $paymentMethod${notes.isNotEmpty ? ' - $notes' : ''}',
        )
        .catchError((error) {
          // Silently handle telegram errors to prevent app freeze
          print('Telegram notification error: $error');
        });
  }

  // Method specifically for subscription renewal with proper notifications
  Future<void> updateSubscriberWithRenewal(
    SubscriberModel updatedSubscriber,
    String packageName,
    double packagePrice,
    bool isPaid,
  ) async {
    // Update the subscriber in database
    if (_useSQLite) {
      await FirebaseService().updateSubscriber(updatedSubscriber.toJson());
    } else {
      final subscribers = await getSubscribersFire();
      final index = subscribers.indexWhere((s) => s.id == updatedSubscriber.id);
      if (index != -1) {
        subscribers[index] = updatedSubscriber;
        // Use individual update instead of saveSubscribers to prevent data loss
        final prefs = await SharedPreferences.getInstance();
        final subscribersJson = jsonEncode(
          subscribers.map((sub) => sub.toJson()).toList(),
        );
        await prefs.setString(_subscribersKey, subscribersJson);
      }
    } // Send specific Telegram notification for renewal (non-blocking)
    TelegramService()
        .sendSubscriberNotification(
          action: 'تجديد اشتراك',
          subscriberName: updatedSubscriber.fullName,
          subscriberPhone: updatedSubscriber.phoneNumber,
          packageName: packageName,
          amount: isPaid ? packagePrice : null,
          additionalInfo: isPaid ? 'مدفوع' : 'غير مدفوع',
        )
        .catchError((error) {
          // Silently handle telegram errors to prevent app freeze
          print('Telegram notification error: $error');
        });
  }

  // update package
  Future<void> updatePackage(PackageModel package) async {
    try {
      await FirebaseFirestore.instance
          .collection('packages')
          .doc(package.id) // لازم الـ id يكون مضبوط
          .update(package.toMap());
    } catch (e) {
      print('Error updating package: $e');
    }
  }

  // Method specifically for editing subscriber data with proper notifications
  Future<void> updateSubscriberData(
    SubscriberModel updatedSubscriber,
    SubscriberModel? oldSubscriber,
  ) async {
    try {
      // Update the subscriber in database
      await _firestore
          .collection('subscribers')
          .doc(updatedSubscriber.id)
          .update(updatedSubscriber.toMap());
      // Determine specific action based on what changed
      String specificAction = 'تعديل بيانات';
      if (oldSubscriber != null) {
        if (oldSubscriber.packageId != updatedSubscriber.packageId) {
          specificAction = 'تغيير باقة';
        } else if (oldSubscriber.isActive != updatedSubscriber.isActive) {
          specificAction = updatedSubscriber.isActive ? 'تفعيل' : 'إلغاء تفعيل';
        } else if (oldSubscriber.phoneNumber != updatedSubscriber.phoneNumber) {
          specificAction = 'تغيير رقم هاتف';
        } else if (oldSubscriber.fullName != updatedSubscriber.fullName) {
          specificAction = 'تغيير اسم المشترك';
        } else if (oldSubscriber.address != updatedSubscriber.address) {
          specificAction = 'تغيير عنوان المشترك';
        } else if (oldSubscriber.technicalNotes !=
            updatedSubscriber.technicalNotes) {
          specificAction = 'تعديل الملاحظات الفنية للمشترك';
        }
      }

      PackageModel? package;
      if (updatedSubscriber.packageId.isNotEmpty) {
        package = await getPackageById(updatedSubscriber.packageId);
      }

      // Send specific Telegram notification for data update (non-blocking)
      // Using unawaited to prevent blocking the UI
      TelegramService()
          .sendSubscriberNotification(
            action: specificAction,
            subscriberName: updatedSubscriber.fullName,
            subscriberPhone: updatedSubscriber.phoneNumber,
            packageName: package?.name,
            amount: package?.price,
          )
          .catchError((error) {
            // Silently handle telegram errors to prevent app freeze
            print('Telegram notification error: $error');
          });
    } catch (e) {
      // Re-throw database errors
      rethrow;
    }
  }

  Future<void> deleteExpense(String expenseId) async {
    try {
      // Get expense details before deletion for notification
      final expenses = await getExpensesFire();
      final expenseToDelete = expenses.firstWhere(
        (e) => e.id == expenseId,
        orElse: () => ExpenseModel(
          adminId: adminId,
          id: '',
          notes: 'مصروف محذوف',
          amount: 0,
          categoryId: '',
          timestamp: DateTime.now(),
        ),
      ); // Changed description to notes, added timestamp

      await FirebaseFirestore.instance
          .collection('expenses')
          .doc(expenseId)
          .delete();

      // Send Telegram notification
      final category = await getExpenseCategoriesFire().then(
        (categories) => categories.firstWhere(
          (cat) => cat.id == expenseToDelete.categoryId,
          orElse: () => ExpenseCategoryModel(
            adminId: adminId,
            id: '',
            name: 'غير محدد',
            createdAt: DateTime.now(),
          ),
        ),
      );

      TelegramService()
          .sendExpenseNotification(
            action: 'حذف مصروف',
            description: expenseToDelete
                .notes, // Changed from expenseToDelete.description to expenseToDelete.notes
            categoryName: category.name,
            amount: expenseToDelete.amount,
            notes: expenseToDelete.notes,
          )
          .catchError((error) {
            // Silently handle telegram errors to prevent app freeze
            print('Telegram notification error: $error');
          });
    } catch (e) {
      // Re-throw database errors
      rethrow;
    }
  }

  Future<List<SubscriberModel>> getDebtors() async {
    final all = await getSubscribersFire();
    return all.where((s) => s.debtAmount > 0).toList();
  }

  // Force database recreation for development/testing
  Future<void> recreateDatabase() async {
    await _sqliteService.recreateDatabase();
  }

  // Debug function to show all subscribers with debts
  Future<void> debugShowAllSubscribersWithDebts() async {
    try {
      print('==== [DEBUG] Showing all subscribers with debts ====');
      final subscribers = await getSubscribers();

      for (var subscriber in subscribers) {
        print('==== [DEBUG] Subscriber: ${subscriber.fullName} ====');
        print('  - ID: ${subscriber.id}');
        print('  - SAS Server ID: ${subscriber.sasServerId}');
        print('  - Username: ${subscriber.username}');
        print('  - Phone: ${subscriber.phoneNumber}');
        print('  - Debt Amount: ${subscriber.debtAmount}');
        print('  - Payment Status: ${subscriber.paymentStatus}');
        print('  - Is Active: ${subscriber.isActive}');
        print('');
      }

      final debtors = subscribers.where((s) => s.debtAmount > 0).toList();
      print(
        '==== [DEBUG] Total subscribers with debts: ${debtors.length} ====',
      );

      if (debtors.isNotEmpty) {
        double totalDebt = debtors.fold(
          0.0,
          (sum, subscriber) => sum + subscriber.debtAmount,
        );
        print(
          '==== [DEBUG] Total debt amount: ${totalDebt.toStringAsFixed(0)} ====',
        );
      }
    } catch (e) {
      print('==== [DEBUG] Error showing subscribers: $e ====');
    }
  }

  // Create test data with debts for debugging sync issues
  Future<void> createTestDataWithDebts() async {
    try {
      print('==== [TEST] Creating test data with debts ====');

      // Create test subscribers with debts
      final testSubscribers = [
        SubscriberModel(
          adminId: user!.uid,
          id: _uuid.v4(),
          fullName: 'أحمد محمد - اختبار',
          phoneNumber: '07901234567',
          packageId: 'test_package_1',
          packageName: 'Economy',
          address: 'بغداد - الكرادة',
          paymentStatus: PaymentStatus.overdue,
          subscriptionStart: DateTime.now().subtract(const Duration(days: 30)),
          subscriptionEnd: DateTime.now().add(const Duration(days: 30)),
          macAddress: '00:11:22:33:44:55',
          routerName: 'Router-001',
          technicalNotes: 'عميل اختبار',
          debtAmount: 50000.0, // مبلغ للاختبار
          createdAt: DateTime.now(),
          isActive: true,
          subscriptionType: SubscriptionType.broadband,
          username: 'test_user_1@ankk',
          password: 'test123',
          sasServerId: "999001",
        ),
        SubscriberModel(
          adminId: user!.uid,
          id: _uuid.v4(),
          fullName: 'فاطمة علي - اختبار',
          phoneNumber: '07912345678',
          packageId: 'test_package_2',
          packageName: 'Standard',
          address: 'بغداد - الجادرية',
          paymentStatus: PaymentStatus.overdue,
          subscriptionStart: DateTime.now().subtract(const Duration(days: 45)),
          subscriptionEnd: DateTime.now().add(const Duration(days: 15)),
          macAddress: '00:22:33:44:55:66',
          routerName: 'Router-002',
          technicalNotes: 'عميلة اختبار',
          debtAmount: 75000.0, // مبلغ للاختبار
          createdAt: DateTime.now(),
          isActive: true,
          subscriptionType: SubscriptionType.broadband,
          username: 'test_user_2@ankk',
          password: 'test456',
          sasServerId: "999002",
        ),
      ];

      // Insert test subscribers
      for (var subscriber in testSubscribers) {
        await _sqliteService.insertSubscriber(subscriber.toJson());
        print(
          '==== [TEST] Created subscriber: ${subscriber.fullName} with debt: ${subscriber.debtAmount} ====',
        );
      }

      print('==== [TEST] Test data creation completed ====');
    } catch (e) {
      print('==== [TEST] Error creating test data: $e ====');
    }
  }

  Future<List<SubscriberModel>> getAllSubscribersFire() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('subscribers')
          .get();
      return snapshot.docs.map((doc) {
        return SubscriberModel.fromMap(doc.data());
      }).toList();
    } catch (e) {
      print('Error fetching all subscribers from Firestore: $e');
      return [];
    }
  }

  /// Authenticate a subscriber by username and password (searches all subscribers, no adminId filter)
  Future<SubscriberModel?> authenticateSubscriber(String username, String password) async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('subscribers')
          .where('username', isEqualTo: username)
          .where('password', isEqualTo: password)
          .limit(1)
          .get();
      if (snapshot.docs.isNotEmpty) {
        return SubscriberModel.fromMap(snapshot.docs.first.data());
      }
      return null;
    } catch (e) {
      print('Error authenticating subscriber: $e');
      return null;
    }
  }
}
