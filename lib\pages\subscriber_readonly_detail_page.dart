import 'package:flutter/material.dart';
import '../models/subscriber_model.dart';
import '../models/package_model.dart';
import '../models/activity_log_model.dart';
import '../models/payment_record_model.dart';
import 'package:intl/intl.dart';
import 'package:isp_manager/services/app_settings_service.dart';
import '../widgets/currency_country_widgets.dart';
import 'subscriber_payment_page.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/activation_card_model.dart';
import '../services/database_service.dart';
import '../services/sas_api_service.dart';
import 'support_chat_page.dart';

class SubscriberReadonlyDetailPage extends StatefulWidget {
  final SubscriberModel subscriber;
  final PackageModel? package;
  final List<ActivityLogModel> activityLogs;
  final List<PaymentRecordModel> paymentRecords;

  const SubscriberReadonlyDetailPage({
    Key? key,
    required this.subscriber,
    this.package,
    required this.activityLogs,
    required this.paymentRecords,
  }) : super(key: key);

  @override
  _SubscriberReadonlyDetailPageState createState() => _SubscriberReadonlyDetailPageState();
}

class _SubscriberReadonlyDetailPageState extends State<SubscriberReadonlyDetailPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل المشترك'),
        backgroundColor: Theme.of(context).colorScheme.surface,
      ),
      body: Column(
        children: [
          _buildHeader(context),
          _buildTabBar(context, _tabController),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildInfoTab(context),
                _buildPaymentsTab(context),
                _buildActivityTab(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    Color statusColor;
    String statusText = widget.subscriber.subscriptionStatusText;
    IconData statusIcon;
    if (widget.subscriber.isExpired) {
      statusColor = Theme.of(context).colorScheme.error;
      statusIcon = Icons.error_outline;
    } else if (widget.subscriber.isExpiringSoon) {
      statusColor = Colors.orange;
      statusIcon = Icons.warning_amber;
    } else {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle_outline;
    }
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            statusColor.withOpacity(0.1),
            Theme.of(context).colorScheme.surface,
          ],
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(statusIcon, color: statusColor, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.subscriber.fullName,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.subscriber.phoneNumber,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              statusText,
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: statusColor,
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(BuildContext context, TabController tabController) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: tabController,
        labelColor: Theme.of(context).colorScheme.primary,
        unselectedLabelColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
        indicatorColor: Theme.of(context).colorScheme.primary,
        tabs: const [
          Tab(text: 'المعلومات'),
          Tab(text: 'المدفوعات'),
          Tab(text: 'السجل'),
        ],
      ),
    );
  }

  Widget _buildInfoTab(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // زر الدعم الفني
          Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                icon: const Icon(Icons.support_agent),
                label: const Text('الدعم الفني'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 14),
                ),
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (_) => SupportChatPage(
                        subscriber: widget.subscriber,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          // زر تفعيل الاشتراك ببطاقة
          Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                icon: const Icon(Icons.credit_card),
                label: const Text('تفعيل الاشتراك ببطاقة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.deepOrange,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 14),
                ),
                onPressed: () => _showActivationCardDialog(context),
              ),
            ),
          ),
          _buildDebtSection(context),
          const SizedBox(height: 20),
          _buildInfoSection(
            context: context,
            title: 'معلومات الاشتراك',
            icon: Icons.inventory,
            children: [
              _buildInfoRow(
                context,
                'الباقة',
                widget.subscriber.packageName,
              ),
              _buildInfoRowWithWidget(
                context,
                'السعر',
                widget.package != null
                    ? CurrencyText(
                        amount: widget.package!.sellingPrice ?? widget.package!.price,
                      )
                    : const Text('غير محدد'),
              ),
              _buildInfoRow(context, 'المدة', widget.package?.durationDisplayText ?? 'غير متوفر'),
              _buildInfoRow(context, 'السرعة', widget.package?.speed ?? 'غير متوفر'),
              _buildInfoRow(context, 'عدد الأجهزة', widget.package != null ? '${widget.package!.deviceCount}' : 'غير متوفر'),
              _buildInfoRow(
                context,
                'تاريخ البداية',
                _formatDateTime(widget.subscriber.subscriptionStart),
              ),
              _buildInfoRow(
                context,
                'تاريخ الانتهاء',
                _formatDateTime(widget.subscriber.subscriptionEnd),
              ),
              _buildInfoRow(
                context,
                'الأيام المتبقية',
                widget.subscriber.daysRemaining != null
                    ? '${widget.subscriber.daysRemaining}'
                    : 'غير محدد',
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildInfoSection(
            context: context,
            title: 'معلومات الاتصال',
            icon: Icons.contact_phone,
            children: [
              _buildInfoRow(context, 'الاسم الكامل', widget.subscriber.fullName),
              _buildInfoRowWithWidget(
                context,
                'رقم الهاتف',
                PhoneText(phoneNumber: widget.subscriber.phoneNumber),
              ),
              _buildInfoRow(context, 'العنوان', widget.subscriber.address),
            ],
          ),
          const SizedBox(height: 20),
          _buildInfoSection(
            context: context,
            title: 'المعلومات الفنية',
            icon: Icons.router,
            children: [
              _buildInfoRow(
                context,
                'نوع الاشتراك',
                widget.subscriber.subscriptionTypeDisplayText,
              ),
              _buildInfoRow(
                context,
                'اسم المستخدم',
                widget.subscriber.username.isEmpty
                    ? 'غير محدد'
                    : widget.subscriber.username,
              ),
              _buildInfoRow(
                context,
                'كلمة المرور',
                widget.subscriber.password.isEmpty
                    ? 'غير محدد'
                    : widget.subscriber.password,
              ),
              _buildInfoRow(
                context,
                'عنوان MAC',
                widget.subscriber.macAddress.isEmpty
                    ? 'غير محدد'
                    : widget.subscriber.macAddress,
              ),
              _buildInfoRow(
                context,
                'اسم الراوتر',
                widget.subscriber.routerName.isEmpty
                    ? 'غير محدد'
                    : widget.subscriber.routerName,
              ),
              _buildInfoRow(
                context,
                'الملاحظات الفنية',
                widget.subscriber.technicalNotes.isEmpty
                    ? 'لا توجد ملاحظات'
                    : widget.subscriber.technicalNotes,
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showActivationCardDialog(BuildContext context) {
    final codeController = TextEditingController();
    bool isLoading = false;
    String? errorText;
    showDialog(
      context: context,
      barrierDismissible: !isLoading,
      builder: (ctx) {
        return StatefulBuilder(
          builder: (ctx, setState) => AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            title: const Text('تفعيل الاشتراك ببطاقة'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: codeController,
                  decoration: InputDecoration(
                    labelText: 'كود البطاقة',
                    errorText: errorText,
                  ),
                  enabled: !isLoading,
                  autofocus: true,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: isLoading ? null : () => Navigator.of(ctx).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: isLoading
                    ? null
                    : () async {
                        setState(() => isLoading = true);
                        final code = codeController.text.trim();
                        if (code.isEmpty) {
                          setState(() {
                            errorText = 'يرجى إدخال كود البطاقة';
                            isLoading = false;
                          });
                          return;
                        }
                        try {
                          final doc = await FirebaseFirestore.instance.collection('activation_cards').doc(code).get();
                          if (!doc.exists) {
                            setState(() {
                              errorText = 'كود البطاقة غير صحيح';
                              isLoading = false;
                            });
                            return;
                          }
                          final card = ActivationCardModel.fromMap(doc.data()!);
                          final adminId = widget.subscriber.adminId;
                          if (card.adminId != adminId) {
                            setState(() {
                              errorText = 'هذه البطاقة لا تخص مديرك';
                              isLoading = false;
                            });
                            return;
                          }
                          if (card.isUsed) {
                            setState(() {
                              errorText = 'تم استخدام هذه البطاقة مسبقاً';
                              isLoading = false;
                            });
                            return;
                          }
                          // تنفيذ منطق التفعيل (تجديد الاشتراك)
                          setState(() => errorText = null);
                          final now = DateTime.now();
                          int? durationDays = int.tryParse(card.value);
                          PackageModel? usedPackage = widget.package;
                          // إذا كانت قيمة البطاقة ليست رقم، اعتبرها اسم باقة
                          if (durationDays == null) {
                            // جلب الباقات من DatabaseService
                            final db = DatabaseService();
                            await db.loadAdmin();
                            final packages = await db.getPackagesFire();
                            try {
                              usedPackage = packages.firstWhere(
                                (p) => p.name.trim() == card.value.trim(),
                              );
                              durationDays = usedPackage.durationInDays;
                            } catch (e) {
                              setState(() {
                                errorText = 'لم يتم العثور على باقة مطابقة لاسم البطاقة "${card.value}"';
                                isLoading = false;
                              });
                              return;
                            }
                          } else {
                            // إذا كانت البطاقة للمدة، تأكد من وجود باقة حالية
                            if (usedPackage == null) {
                              setState(() {
                                errorText = 'لا يمكن استخدام بطاقة مدة لمشترك بدون باقة حالية.';
                                isLoading = false;
                              });
                              return;
                            }
                          }
                          if (durationDays == null || durationDays <= 0) {
                            setState(() {
                              errorText = 'مدة الباقة غير معرفة أو غير صالحة';
                              isLoading = false;
                            });
                            return;
                          }
                          final packageId = usedPackage?.id ?? widget.subscriber.packageId;
                          final packageName = usedPackage?.name ?? widget.subscriber.packageName;
                          if (packageId == null || packageName == null) {
                            setState(() {
                              errorText = 'لا يمكن تحديد باقة المشترك بشكل صحيح.';
                              isLoading = false;
                            });
                            return;
                          }
                          final newEnd = now.add(Duration(days: durationDays));
                          final updatedSubscriber = widget.subscriber.copyWith(
                            subscriptionStart: now,
                            subscriptionEnd: newEnd,
                            packageId: packageId,
                            packageName: packageName,
                            adminId: widget.subscriber.adminId, // إصلاح الخطأ
                          );
                          // تحديث بيانات المشترك
                          await FirebaseFirestore.instance
                              .collection('subscribers')
                              .doc(widget.subscriber.id)
                              .update({
                            'subscriptionStart': now.toIso8601String(),
                            'subscriptionEnd': newEnd.toIso8601String(),
                            'packageId': packageId,
                            'packageName': packageName,
                          });
                          // تحديث البطاقة كـ مستخدمة
                          await FirebaseFirestore.instance
                              .collection('activation_cards')
                              .doc(card.id)
                              .update({
                            'isUsed': true,
                            'usedBy': widget.subscriber.id,
                            'usedAt': now.toIso8601String(),
                          });
                          // تفعيل SAS إذا كانت الباقة تحتوي sasProfileId
                          String sasMessage = '';
                          if (usedPackage != null && usedPackage.sasProfileId != null && usedPackage.sasProfileId!.isNotEmpty) {
                            try {
                              final sasApi = SasApiService();
                              await sasApi.login();
                              final sasResponse = await sasApi.activateUserInSas(
                                username: widget.subscriber.username,
                                newProfileId: int.parse(usedPackage.sasProfileId!),
                                comments: 'تفعيل اشتراك ببطاقة من تطبيق ISP Manager - ${widget.subscriber.fullName}',
                                moneyCollected: true,
                                issueInvoice: true,
                              );
                              if (sasResponse != null && sasResponse['status'] == 'success') {
                                sasMessage = '\nتم تفعيل الاشتراك في SAS Radius بنجاح.';
                              } else {
                                sasMessage = '\nتم التفعيل محلياً، لكن فشل التفعيل في SAS: ${sasResponse?['message'] ?? 'غير معروف'}';
                              }
                            } catch (e) {
                              sasMessage = '\nتم التفعيل محلياً، لكن حدث خطأ في التفعيل في SAS: $e';
                            }
                          }
                          if (mounted) {
                            Navigator.of(ctx).pop();
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text('تم تفعيل الاشتراك بنجاح!$sasMessage')),
                            );
                          }
                        } catch (e) {
                          setState(() {
                            errorText = 'حدث خطأ: $e';
                            isLoading = false;
                          });
                        }
                      },
                child: isLoading
                    ? const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2))
                    : const Text('تفعيل'),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPaymentsTab(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              icon: const Icon(Icons.account_balance_wallet),
              label: const Text('الدفع الإلكتروني'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 14),
              ),
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (_) => SubscriberPaymentPage(subscriber: widget.subscriber),
                  ),
                );
              },
            ),
          ),
        ),
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: widget.subscriber.debtStatusColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: widget.subscriber.debtStatusColor.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                widget.subscriber.debtStatusIcon,
                color: widget.subscriber.debtStatusColor,
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.subscriber.hasOutstandingDebt
                          ? 'إجمالي الدين المستحق'
                          : widget.subscriber.hasAdvancePayment
                          ? 'رصيد مدفوع مقدماً'
                          : 'حالة الدين',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: widget.subscriber.debtStatusColor,
                      ),
                    ),
                    FutureBuilder<String>(
                      future: widget.subscriber.hasOutstandingDebt
                          ? AppSettingsService.formatCurrency(
                              widget.subscriber.debtAmount,
                            )
                          : widget.subscriber.hasAdvancePayment
                          ? AppSettingsService.formatCurrency(
                              widget.subscriber.advancePaymentAmount,
                            )
                          : Future.value('لا توجد ديون مستحقة'),
                      builder: (context, snapshot) {
                        return Text(
                          snapshot.data ?? '...',
                          style: Theme.of(context).textTheme.headlineSmall
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: widget.subscriber.debtStatusColor,
                              ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: widget.paymentRecords.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.payment,
                        size: 64,
                        color: Theme.of(
                          context,
                        ).colorScheme.primary.withOpacity(0.5),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد مدفوعات مسجلة',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: Theme.of(
                                context,
                              ).colorScheme.onSurface.withOpacity(0.6),
                            ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: widget.paymentRecords.length,
                  itemBuilder: (context, index) {
                    final record = widget.paymentRecords[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.green.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(Icons.check_circle, color: Colors.green),
                        ),
                        title: FutureBuilder<String>(
                          future: AppSettingsService.formatCurrency(
                            record.amount,
                          ),
                          builder: (context, snapshot) {
                            return Text(
                              snapshot.data ?? '...',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            );
                          },
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('طريقة الدفع: ${record.paymentMethod}'),
                            Text(_formatDate(record.paymentDate)),
                            if (record.notes != null &&
                                record.notes!.isNotEmpty)
                              Text('ملاحظات: ${record.notes}'),
                          ],
                        ),
                        isThreeLine: true,
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildActivityTab(BuildContext context) {
    return widget.activityLogs.isEmpty
        ? Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.history,
                  size: 64,
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'لا توجد أنشطة مسجلة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          )
        : ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: widget.activityLogs.length,
            itemBuilder: (context, index) {
              final log = widget.activityLogs[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Theme.of(
                        context,
                      ).colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getActivityIcon(log.action),
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  title: Text(
                    log.action,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(log.description),
                      Text(_formatDateTime(log.timestamp)),
                      if (log.amount > 0)
                        FutureBuilder<String>(
                          future: AppSettingsService.formatCurrency(log.amount),
                          builder: (context, snapshot) {
                            return Text(
                              'المبلغ: ${snapshot.data ?? '...'}',
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.primary,
                                fontWeight: FontWeight.w500,
                              ),
                            );
                          },
                        ),
                    ],
                  ),
                  isThreeLine: true,
                ),
              );
            },
          );
  }

  Widget _buildInfoSection({
    required BuildContext context,
    required String title,
    required IconData icon,
    required List<Widget> children,
    Color? iconColor,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(icon, color: iconColor ?? Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRowWithWidget(BuildContext context, String label, Widget valueWidget) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ),
          Expanded(child: valueWidget),
        ],
      ),
    );
  }

  Widget _buildDebtSection(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.subscriber.debtStatusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: widget.subscriber.debtStatusColor.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                widget.subscriber.debtStatusIcon,
                color: widget.subscriber.debtStatusColor,
              ),
              const SizedBox(width: 8),
              Text(
                widget.subscriber.hasOutstandingDebt
                    ? 'دين مستحق'
                    : widget.subscriber.hasAdvancePayment
                    ? 'رصيد مقدم'
                    : 'حالة الدين',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: widget.subscriber.debtStatusColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            widget.subscriber.debtStatusText,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: widget.subscriber.debtStatusColor,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getActivityIcon(String action) {
    switch (action) {
      case 'إضافة مشترك':
        return Icons.person_add;
      case 'تجديد اشتراك':
        return Icons.refresh;
      case 'تسجيل دفعة':
        return Icons.payment;
      case 'تعديل بيانات':
        return Icons.edit;
      case 'إضافة دين سابق':
        return Icons.add_card;
      default:
        return Icons.history;
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'غير محدد';
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return 'غير محدد';
    return DateFormat('yyyy/MM/dd hh:mm a').format(dateTime);
  }
} 