import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:isp_manager/models/payment_record_model.dart';
import 'package:isp_manager/services/sas_api_service.dart';
import '../services/database_service.dart';
import '../services/firebase_auth_service.dart';
import '../services/app_settings_service.dart';
import '../services/message_service.dart';
import '../models/subscriber_model.dart';
import '../models/package_model.dart';
import '../models/user_model.dart';
import '../models/activity_log_model.dart';
import '../widgets/currency_country_widgets.dart';
import 'subscriber_detail_page.dart';
import 'message_templates_page.dart';
import '../models/message_template_model.dart';

class SubscribersPage extends StatefulWidget {
  const SubscribersPage({super.key});

  @override
  State<SubscribersPage> createState() => _SubscribersPageState();
}

enum SubscriberSortOption {
  none,
  subscriptionEndDateAsc,
  subscriptionEndDateDesc,
  debtAmountAsc,
  debtAmountDesc,
}

final currentUser = FirebaseAuth.instance.currentUser;

class _SubscribersPageState extends State<SubscribersPage>
    with TickerProviderStateMixin {
  List<SubscriberModel> _subscribers = [];
  List<SubscriberModel> _filteredSubscribers = [];
  List<PackageModel> _packages = [];
  UserModel? _currentUser;
  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedStatus = 'الكل';
  SubscriberSortOption _currentSortOption =
      SubscriberSortOption.none; // New state variable
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  final List<String> _statusFilters = ['الكل', 'نشط', 'منتهي', 'ينتهي قريباً'];

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    _loadData();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  // دالة مساعدة لإظهار الرسائل بشكل آمن
  void _showSafeSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : null,
        ),
      );
    } catch (scaffoldError) {
      print(message);
      // إظهار Toast كبديل
      try {
        Fluttertoast.showToast(
          msg: message,
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: isError ? Colors.red : Colors.green,
          textColor: Colors.white,
        );
      } catch (toastError) {
        print('فشل في إظهار Toast: $toastError');
      }
    }
  }

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  Future<void> _loadData() async {
    print('==== [LOAD] _loadData CALLED ====');
    if (!mounted) return;
    setState(() => _isLoading = true);

    try {
      final databaseService = DatabaseService();
      final authService = FirebaseAuthService();

      // تحميل بيانات المستخدم من Firebase
      final firebaseUser = authService.currentUser;
      UserModel? currentUser;

      if (firebaseUser != null) {
        currentUser = await authService.getUserData(firebaseUser.uid);
      }

      // Load data from local database only (no automatic synchronization)
      await DatabaseService().syncSubscribersToFirebase();
      var subscribers = await databaseService.getSubscribersFire();
      if (subscribers.isEmpty) {
        subscribers = await DatabaseService().getSubscribers();
      }
      var packages = await databaseService.getPackagesFire();
      if (packages.isEmpty) {
        packages = await DatabaseService().getPackages();
      }
      
      if (!mounted) return;
      setState(() {
        _currentUser = currentUser;
        _subscribers = subscribers;
        _packages = packages;
        _isLoading = false;
      });

      _applyFilters();
      if (mounted) {
        _fadeController.forward();
      }
    } catch (e) {
      print('Error loading data: $e');
      if (!mounted) return;
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _applyFilters() {
    if (!mounted) return;
    setState(() {
      _filteredSubscribers = _subscribers.where((subscriber) {
        // Search filter
        final matchesSearch =
            _searchQuery.isEmpty ||
            subscriber.fullName.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            subscriber.phoneNumber.contains(_searchQuery);

        // Status filter
        bool matchesStatus = true;
        switch (_selectedStatus) {
          case 'نشط':
            matchesStatus = !subscriber.isExpired;
            break;
          case 'منتهي':
            matchesStatus = subscriber.isExpired;
            break;
          case 'ينتهي قريباً':
            matchesStatus = subscriber.isExpiringSoon;
            break;
        }

        return matchesSearch && matchesStatus;
      }).toList();

      // Apply sorting based on _currentSortOption
      switch (_currentSortOption) {
        case SubscriberSortOption.subscriptionEndDateAsc:
          _filteredSubscribers.sort((a, b) {
            if (a.subscriptionEnd == null && b.subscriptionEnd == null) {
              return 0;
            }
            if (a.subscriptionEnd == null) return 1; // Nulls at the end
            if (b.subscriptionEnd == null) return -1; // Nulls at the end
            return a.subscriptionEnd!.compareTo(b.subscriptionEnd!);
          });
          break;
        case SubscriberSortOption.subscriptionEndDateDesc:
          _filteredSubscribers.sort((a, b) {
            if (a.subscriptionEnd == null && b.subscriptionEnd == null) {
              return 0;
            }
            if (a.subscriptionEnd == null) return -1; // Nulls at the end
            if (b.subscriptionEnd == null) return 1; // Nulls at the end
            return b.subscriptionEnd!.compareTo(
              a.subscriptionEnd!,
            ); // Descending
          });
          break;
        case SubscriberSortOption.debtAmountAsc:
          _filteredSubscribers.sort(
            (a, b) => a.debtAmount.compareTo(b.debtAmount),
          );
          break;
        case SubscriberSortOption.debtAmountDesc:
          _filteredSubscribers.sort(
            (a, b) => b.debtAmount.compareTo(a.debtAmount),
          );
          break;
        case SubscriberSortOption.none:
          // No specific sorting, keep default order (which is currently by subscription end date ascending)
          // If no specific sort is selected, we can revert to the original loading order or a default.
          // For now, I'll keep the existing sort by subscription end date ascending as a default if 'none' is selected.
          _filteredSubscribers.sort((a, b) {
            if (a.subscriptionEnd == null && b.subscriptionEnd == null) {
              return 0;
            }
            if (a.subscriptionEnd == null) return 1;
            if (b.subscriptionEnd == null) return -1;
            return a.subscriptionEnd!.compareTo(b.subscriptionEnd!);
          });
          break;
      }
    });
  }

  void _onSearchChanged(String query) {
    if (!mounted) return;
    setState(() {
      _searchQuery = query;
    });
    _applyFilters();
  }

  void _onStatusFilterChanged(String status) {
    if (!mounted) return;
    setState(() {
      _selectedStatus = status;
    });
    _applyFilters();
  }

  Future<void> _showAddSubscriberDialog() async {
    // التحقق من الصلاحيات
    if (!(_currentUser?.hasPermission(Permission.addSubscribers) ?? false)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('ليس لديك صلاحية لإضافة مشتركين'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
      return;
    }

    if (_packages.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('يجب إضافة باقات أولاً قبل إضافة مشتركين'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
      return;
    }

    await showDialog(
      context: context,
      builder: (context) => _AddSubscriberDialog(
        packages: _packages,
        onSubscriberAdded: _loadData,
        adminId: _currentUser?.id,
      ),
    );
  }

  PackageModel? _getPackageById(String packageId) {
    try {
      return _packages.firstWhere((p) => p.id == packageId);
    } catch (e) {
      return null;
    }
  }

  // Manual synchronization function - called only when user explicitly requests sync
  Future<void> _syncWithSas() async {
    if (!mounted) return;
    setState(() => _isLoading = true);

    try {
      final databaseService = DatabaseService();

      // Show loading message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('جاري المزامنة مع SAS Radius...'),
            backgroundColor: Theme.of(context).colorScheme.primary,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
          ),
        );
      }

      // Attempt to synchronize from SAS API
      final syncSuccess = await databaseService.syncFromSas();

      if (syncSuccess) {
        // Reload data after successful sync
        await _loadData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('تمت المزامنة مع SAS Radius بنجاح'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } else {
        if (!mounted) return;
        setState(() => _isLoading = false);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                'فشلت المزامنة مع SAS Radius. تأكد من إعدادات الاتصال',
              ),
              backgroundColor: Theme.of(context).colorScheme.error,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      if (!mounted) return;
      setState(() => _isLoading = false);
      print('Error during SAS synchronization: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في المزامنة: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  /// إظهار dialog لتسجيل دفعة جديدة
  void _showPaymentDialog(SubscriberModel subscriber) async {
    final amountController = TextEditingController();
    final notesController = TextEditingController();
    bool isLoading = false;
    // استخدم showDialog وأرجع قيمة عند النجاح
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text('تسجيل دفعة - ${subscriber.fullName}'),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // عرض الحالة الحالية
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: subscriber.debtStatusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: subscriber.debtStatusColor.withOpacity(0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          subscriber.debtStatusIcon,
                          color: subscriber.debtStatusColor,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'الحالة الحالية: ${subscriber.debtStatusText}',
                            style: TextStyle(
                              color: subscriber.debtStatusColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: amountController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText:
                          'مبلغ الدفعة (${AppSettingsService.getCurrencySymbol()})',
                      border: const OutlineInputBorder(),
                      prefixIcon: const Icon(Icons.payments),
                      hintText: 'أدخل المبلغ المدفوع',
                    ),
                    validator: (value) {
                      if (value?.isEmpty ?? true) return 'يرجى إدخال المبلغ';
                      if (double.tryParse(value!) == null) {
                        return 'يرجى إدخال رقم صحيح';
                      }
                      if (double.parse(value) <= 0) {
                        return 'يجب أن يكون المبلغ أكبر من صفر';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: notesController,
                    decoration: const InputDecoration(
                      labelText: 'ملاحظات (اختياري)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.note),
                      hintText: 'أي ملاحظات إضافية...',
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info, color: Colors.blue, size: 16),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'سيتم خصم المبلغ من الديون المستحقة. إذا كان المبلغ أكبر من الدين، سيصبح دفع مقدم.',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.blue[700],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: isLoading ? null : () => Navigator.pop(context, false),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: isLoading
                  ? null
                  : () async {
                      final amount = double.tryParse(amountController.text);
                      if (amount == null || amount <= 0) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('يرجى إدخال مبلغ صحيح')),
                        );
                        return;
                      }
                      setDialogState(() => isLoading = true);
                      try {
                        final newDebtAmount = subscriber.debtAmount - amount;
                        final updatedSubscriber = subscriber.copyWith(
                          adminId: DatabaseService().adminId,
                          debtAmount: newDebtAmount,
                        );
                        await DatabaseService().updateSubscriber(
                          updatedSubscriber,
                          isSyncUpdate: false,
                        );
                        await DatabaseService().addPaymentRecord(
                          PaymentRecordModel(
                            id: _firestore.collection('paymentRecord').doc().id,
                            subscriberId: updatedSubscriber.id,
                            amount: amount,
                            paymentMethod: 'نقداً',
                            paymentDate: DateTime.now(),
                            recordedBy: FirebaseAuth.instance.currentUser!.uid,
                            adminId: DatabaseService().adminId,
                          ),
                        );
                        Navigator.pop(context, true); // أرجع true عند النجاح
                      } catch (e) {
                        Navigator.pop(context, false);
                        if (mounted) {
                          _showSafeSnackBar(
                            'خطأ في تسجيل الدفعة: $e',
                            isError: true,
                          );
                        }
                      }
                    },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Text('تسجيل الدفعة'),
            ),
          ],
        ),
      ),
    );

    // بعد إغلاق الـ dialog، إذا تم تسجيل الدفعة بنجاح، أعد تحميل البيانات وأظهر رسالة
    if (result == true) {
      await _loadData();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تسجيل الدفعة بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'إدارة المشتركين',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  onChanged: _onSearchChanged,
                  decoration: InputDecoration(
                    hintText: 'البحث بالاسم أو رقم الهاتف...',
                    prefixIcon: Icon(
                      Icons.search,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).colorScheme.outline,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(
                          context,
                        ).colorScheme.outline.withOpacity(0.5),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).colorScheme.primary,
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Theme.of(context).colorScheme.surface,
                  ),
                ),
                const SizedBox(height: 12),

                // Status Filter Chips
                SizedBox(
                  height: 40,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _statusFilters.length,
                    itemBuilder: (context, index) {
                      final status = _statusFilters[index];
                      final isSelected = _selectedStatus == status;

                      return Padding(
                        padding: const EdgeInsets.only(left: 8),
                        child: FilterChip(
                          label: Text(
                            status,
                            style: TextStyle(
                              color: isSelected
                                  ? Theme.of(context).colorScheme.onPrimary
                                  : Theme.of(context).colorScheme.onSurface,
                              fontWeight: isSelected
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                            ),
                          ),
                          selected: isSelected,
                          onSelected: (_) => _onStatusFilterChanged(status),
                          backgroundColor: Theme.of(
                            context,
                          ).colorScheme.surface,
                          selectedColor: Theme.of(context).colorScheme.primary,
                          checkmarkColor: Theme.of(
                            context,
                          ).colorScheme.onPrimary,
                          side: BorderSide(
                            color: isSelected
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(
                                    context,
                                  ).colorScheme.outline.withOpacity(0.5),
                          ),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 12),
                // Sort Options Dropdown
                DropdownButtonFormField<SubscriberSortOption>(
                  value: _currentSortOption,
                  decoration: InputDecoration(
                    labelText: 'ترتيب حسب',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).colorScheme.outline,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(
                          context,
                        ).colorScheme.outline.withOpacity(0.5),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).colorScheme.primary,
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Theme.of(context).colorScheme.surface,
                  ),
                  items: const [
                    DropdownMenuItem(
                      value: SubscriberSortOption.none,
                      child: Text('لا شيء'),
                    ),
                    DropdownMenuItem(
                      value: SubscriberSortOption.subscriptionEndDateAsc,
                      child: Text('تاريخ الانتهاء (تصاعدي)'),
                    ),
                    DropdownMenuItem(
                      value: SubscriberSortOption.subscriptionEndDateDesc,
                      child: Text('تاريخ الانتهاء (تنازلي)'),
                    ),
                    DropdownMenuItem(
                      value: SubscriberSortOption.debtAmountAsc,
                      child: Text('مبلغ الدين (تصاعدي)'),
                    ),
                    DropdownMenuItem(
                      value: SubscriberSortOption.debtAmountDesc,
                      child: Text('مبلغ الدين (تنازلي)'),
                    ),
                  ],
                  onChanged: (value) {
                    if (!mounted) return;
                    setState(() {
                      _currentSortOption = value!;
                    });
                    _applyFilters();
                  },
                ),
              ],
            ),
          ),

          // Subscribers List
          Expanded(
            child: _isLoading
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'جاري تحميل المشتركين...',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  )
                : _filteredSubscribers.isEmpty
                ? _buildEmptyState()
                : FadeTransition(
                    opacity: _fadeAnimation,
                    child: RefreshIndicator(
                      onRefresh: _syncWithSas,
                      child: ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _filteredSubscribers.length,
                        itemBuilder: (context, index) {
                          final subscriber = _filteredSubscribers[index];
                          final package = _getPackageById(subscriber.packageId);

                          return Padding(
                            padding: const EdgeInsets.only(bottom: 12),
                            child: _buildSubscriberCard(subscriber, package),
                          );
                        },
                      ),
                    ),
                  ),
          ),
        ],
      ),
      floatingActionButton:
          (_currentUser?.hasPermission(Permission.addSubscribers) ?? false)
          ? FloatingActionButton.extended(
              onPressed: _showAddSubscriberDialog,

              //  () {

              //   // SasApiService().createUser()
              // },
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
              icon: const Icon(Icons.person_add),
              label: const Text(
                'مشترك جديد',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            )
          : null,
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 80,
            color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty || _selectedStatus != 'الكل'
                ? 'لا توجد نتائج للبحث'
                : 'لا يوجد مشتركين بعد',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty || _selectedStatus != 'الكل'
                ? 'جرب تغيير كلمات البحث أو الفلتر'
                : 'اضغط على زر "مشترك جديد" لإضافة أول مشترك',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriberCard(
    SubscriberModel subscriber,
    PackageModel? package,
  ) {
    Color statusColor;
    String statusText = subscriber.subscriptionStatusText;
    IconData statusIcon;

    if (subscriber.isExpired) {
      statusColor = Theme.of(context).colorScheme.error;
      statusIcon = Icons.error_outline;
    } else if (subscriber.isExpiringSoon) {
      statusColor = Colors.orange;
      statusIcon = Icons.warning_amber;
    } else {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle_outline;
    }

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: statusColor.withOpacity(0.3), width: 1),
      ),
      child: InkWell(
        onTap: () async {
          await Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) =>
                  SubscriberDetailPage(subscriberId: subscriber.id),
            ),
          );
          _loadData();
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(statusIcon, color: statusColor, size: 20),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          subscriber.fullName,
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        PhoneText(
                          phoneNumber: subscriber.phoneNumber,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: Theme.of(
                                  context,
                                ).colorScheme.onSurface.withOpacity(0.6),
                              ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      statusText,
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: statusColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.inventory,
                      label: 'الباقة',
                      value: subscriber.packageName,
                    ),
                  ),
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.calendar_today,
                      label: 'ينتهي في',
                      value:
                          '${subscriber.daysRemaining ?? 'N/A'} يوم', // Added null-check for daysRemaining
                    ),
                  ),
                ],
              ),
              // عرض حالة الدين (دائماً مرئية)
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: subscriber.debtStatusColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: subscriber.debtStatusColor.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      subscriber.debtStatusIcon,
                      color: subscriber.debtStatusColor,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: FutureBuilder<String>(
                        future: subscriber.getDebtStatusText(),
                        builder: (context, snapshot) {
                          return Text(
                            snapshot.data ?? 'جاري التحميل...',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: subscriber.debtStatusColor,
                                  fontWeight: FontWeight.bold,
                                ),
                          );
                        },
                      ),
                    ), // زر إضافة دفعة
                    if (subscriber.hasOutstandingDebt ||
                        subscriber.hasNoDebt) ...[
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: () => _showPaymentDialog(subscriber),
                        icon: Icon(
                          Icons.payment,
                          color: subscriber.debtStatusColor,
                          size: 18,
                        ),
                        tooltip: 'تسجيل دفعة',
                        constraints: const BoxConstraints(
                          minWidth: 32,
                          minHeight: 32,
                        ),
                        padding: EdgeInsets.zero,
                      ),
                    ],
                    // زر إرسال تنبيه انتهاء الاشتراك (يظهر فقط للمشتركين الذين ينتهي اشتراكهم قريباً)
                    if (subscriber.isExpiringSoon) ...[
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: () => _sendExpiryNotification(subscriber),
                        icon: const Icon(
                          Icons.notification_important,
                          color: Colors.orange,
                          size: 18,
                        ),
                        tooltip: 'إرسال تنبيه انتهاء الاشتراك',
                        constraints: const BoxConstraints(
                          minWidth: 32,
                          minHeight: 32,
                        ),
                        padding: EdgeInsets.zero,
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Theme.of(context).colorScheme.primary),
        const SizedBox(width: 6),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withOpacity(0.6),
                ),
                overflow: TextOverflow.ellipsis,
              ),
              Text(
                value,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500),
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  // دالة إرسال تنبيه انتهاء الاشتراك
  void _sendExpiryNotification(SubscriberModel subscriber) async {
    try {
      // عرض loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('جاري إرسال التنبيه...'),
              ],
            ),
          );
        },
      );

      final messageService = MessageService();
      final dbService = DatabaseService();

      // ابحث أولاً عن قالب nearExpiry (قرب انتهاء الاشتراك)
      final nearExpiryTemplate = await dbService.getDefaultMessageTemplate(MessageTemplateType.nearExpiry);
      if (nearExpiryTemplate != null) {
        // استخدم القالب الجديد
        final packages = await dbService.getPackagesFire();
        final package = packages.where((p) => p.id == subscriber.packageId).firstOrNull;
        if (package == null) {
          print('DEBUG: _sendExpiryNotification in subscribers_page.dart: Package not found for subscriber.packageId: ${subscriber.packageId}. Using default package.');
        } else {
          print('DEBUG: _sendExpiryNotification in subscribers_page.dart: Package found: ${package.name} for subscriber.packageId: ${subscriber.packageId}.');
        }
        final defaultPackage = package ?? PackageModel(
          adminId: subscriber.adminId,
          id: 'default',
          name: 'باقة افتراضية',
          price: 0.0,
          durationInDays: 30,
          speed: 'غير محدد',
          deviceCount: 1,
          createdAt: DateTime.now(),
          serverId: '',
        );
        final message = await messageService.previewMessage(
          nearExpiryTemplate.content,
          subscriber,
          defaultPackage,
        );
        await messageService.sendWhatsAppMessage(subscriber, message);
        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم إرسال تنبيه قرب انتهاء الاشتراك للمشترك ${subscriber.fullName} بنجاح'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
        return;
      }

      // إذا لم يوجد قالب nearExpiry، استخدم قالب expiry
      final template = await dbService.getDefaultMessageTemplate(MessageTemplateType.expiry);
      if (template == null) {
        // إنشاء القوالب الافتراضية إذا لم يوجد أي قالب
        await messageService.createDefaultTemplates();
        if (mounted) {
          Navigator.of(context).pop(); // إغلاق Dialog
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('تم إنشاء قوالب التنبيه'),
              content: const Text('تم إنشاء قوالب افتراضية لرسائل التنبيه. يمكنك تعديلها من شاشة إدارة قوالب الرسائل.'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('إغلاق'),
                ),
                TextButton.icon(
                  onPressed: () {
                    Navigator.pop(context);
                    Navigator.of(context).push(
                      MaterialPageRoute(builder: (context) => const MessageTemplatesPage()),
                    );
                  },
                  icon: const Icon(Icons.settings),
                  label: const Text('إدارة القوالب'),
                ),
              ],
            ),
          );
        }
        return;
      }

      // إرسال رسالة انتهاء الاشتراك باستخدام القالب الافتراضي
      await messageService.sendExpiryReminder(subscriber);

      if (mounted) {
        Navigator.of(context).pop(); // إغلاق loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إرسال تنبيه انتهاء الاشتراك للمشترك ${subscriber.fullName} بنجاح',
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // إغلاق loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إرسال التنبيه: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}

class _AddSubscriberDialog extends StatefulWidget {
  final List<PackageModel> packages;
  final VoidCallback onSubscriberAdded;
  final String? adminId;

  const _AddSubscriberDialog({
    required this.packages,
    required this.onSubscriberAdded,
    required this.adminId,
  });

  @override
  State<_AddSubscriberDialog> createState() => _AddSubscriberDialogState();
}

class _AddSubscriberDialogState extends State<_AddSubscriberDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _macController = TextEditingController();
  final _routerController = TextEditingController();
  final _notesController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();

  String? _selectedPackageId;
  PaymentStatus _paymentStatus = PaymentStatus.paid;
  SubscriptionType _subscriptionType = SubscriptionType.broadband;
  bool _isLoading = false;
  DateTime? _selectedStartDate;
  String _countryCode = '+964';
  @override
  void initState() {
    super.initState();
    _loadCountryCode();
    // Initialize with the first package if available
    if (widget.packages.isNotEmpty) {
      _selectedPackageId = widget.packages.first.id;
    }
  }

  Future<void> _loadCountryCode() async {
    final phoneCode = await AppSettingsService.getPhoneCode();
    setState(() {
      _countryCode = phoneCode;
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _macController.dispose();
    _routerController.dispose();
    _notesController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _pickStartDate() async {
    final now = DateTime.now();
    final initialDate = _selectedStartDate ?? now;
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(now.year - 2),
      lastDate: DateTime(now.year + 2),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Theme.of(context).colorScheme.primary,
              onPrimary: Theme.of(context).colorScheme.onPrimary,
              surface: Theme.of(context).dialogBackgroundColor,
              onSurface: Theme.of(context).colorScheme.onSurface,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null) {
      setState(() {
        _selectedStartDate = picked;
      });
    }
  }

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final authService = FirebaseAuthService();
  Future<void> _saveSubscriber() async {
    if (!_formKey.currentState!.validate() || _selectedPackageId == null) {
      return;
    }
    if (widget.adminId == null || widget.adminId!.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تعذر تحديد مدير النظام. يرجى إعادة تسجيل الدخول.'),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
      return;
    }
    try {
      PackageModel? package;
      try {
        package = widget.packages.firstWhere((p) => p.id == _selectedPackageId);
      } catch (e) {
        package = null;
      }
      if (package == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تعذر العثور على الباقة المختارة.'),
              backgroundColor: Theme.of(context).colorScheme.error,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
        return;
      }
      final now = DateTime.now();
      final startDate = _selectedStartDate ?? now;
      final endDate = startDate.add(Duration(days: package.durationInDays));
      final subscriber = SubscriberModel(
        adminId: widget.adminId!,
        id: _firestore.collection('subscribers').doc().id,
        fullName: _nameController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        packageId: _selectedPackageId!,
        packageName: package.name,
        address: _addressController.text.trim(),
        paymentStatus: _paymentStatus,
        subscriptionStart: startDate,
        subscriptionEnd: endDate,
        macAddress: _macController.text.trim(),
        routerName: _routerController.text.trim(),
        technicalNotes: _notesController.text.trim(),
        debtAmount: _paymentStatus == PaymentStatus.paid ? 0.0 : package.price,
        createdAt: now,
        subscriptionType: _subscriptionType,
        username: _usernameController.text.trim(),
        password: _passwordController.text.trim(),
      );
      await DatabaseService().addSubscriber(subscriber, isSyncUpdate: false);
      // Log activity
      final user = await DatabaseService().getCurrentUser();
      if (user != null) {
        final log = ActivityLogModel(
          adminId: subscriber.adminId,
          id: DatabaseService().generateId(),
          subscriberId: subscriber.id,
          userId: user.id,
          action: 'إضافة مشترك',
          description: 'تم إضافة مشترك جديد: ${subscriber.fullName}',
          amount: _paymentStatus == PaymentStatus.paid ? package.price : 0.0,
          timestamp: now,
        );
        await DatabaseService().addActivityLog(log);
      }
      widget.onSubscriberAdded();
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('تم إضافة المشترك بنجاح'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      print(e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة المشترك: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500),
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.person_add,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'إضافة مشترك جديد',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'الاسم الكامل *',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال الاسم الكامل';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _phoneController,
                  decoration: InputDecoration(
                    labelText: 'رقم الهاتف *',
                    border: const OutlineInputBorder(),
                    prefixText: '$_countryCode ',
                    prefixStyle: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  keyboardType: TextInputType.phone,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال رقم الهاتف';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _selectedPackageId,
                  decoration: const InputDecoration(
                    labelText: 'الباقة *',
                    border: OutlineInputBorder(),
                  ),
                  items: widget.packages.map((package) {
                    return DropdownMenuItem(
                      value: package.id,
                      child: PackageDisplayText(package: package),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (!mounted) return;
                    setState(() {
                      _selectedPackageId = value;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'يرجى اختيار الباقة';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                // --- حقل اختيار تاريخ البداية ---
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: _pickStartDate,
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            labelText: 'تاريخ بداية الاشتراك *',
                            border: OutlineInputBorder(),
                          ),
                          child: Text(
                            _selectedStartDate == null
                                ? 'اختر تاريخ البداية'
                                : '${_selectedStartDate!.day}/${_selectedStartDate!.month}/${_selectedStartDate!.year}',
                            style: TextStyle(
                              color: _selectedStartDate == null
                                  ? Colors.grey
                                  : Colors.black,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                TextFormField(
                  controller: _addressController,
                  decoration: const InputDecoration(
                    labelText: 'العنوان *',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال العنوان';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                DropdownButtonFormField<PaymentStatus>(
                  value: _paymentStatus,
                  decoration: const InputDecoration(
                    labelText: 'حالة الدفع',
                    border: OutlineInputBorder(),
                  ),
                  items: PaymentStatus.values.map((status) {
                    String text;
                    switch (status) {
                      case PaymentStatus.paid:
                        text = 'مدفوع';
                        break;
                      case PaymentStatus.pending:
                        text = 'في انتظار الدفع';
                        break;
                      case PaymentStatus.overdue:
                        text = 'متأخر';
                        break;
                    }
                    return DropdownMenuItem(value: status, child: Text(text));
                  }).toList(),
                  onChanged: (value) {
                    if (value != null && mounted) {
                      setState(() {
                        _paymentStatus = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),

                // نوع الاشتراك
                DropdownButtonFormField<SubscriptionType>(
                  value: _subscriptionType,
                  decoration: const InputDecoration(
                    labelText: 'نوع الاشتراك',
                    border: OutlineInputBorder(),
                  ),
                  items: SubscriptionType.values.map((type) {
                    String text;
                    switch (type) {
                      case SubscriptionType.broadband:
                        text = 'برودباند';
                        break;
                      case SubscriptionType.hotspot:
                        text = 'هوت سبوت';
                        break;
                    }
                    return DropdownMenuItem(value: type, child: Text(text));
                  }).toList(),
                  onChanged: (value) {
                    if (value != null && mounted) {
                      setState(() {
                        _subscriptionType = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),

                // اسم المستخدم وكلمة المرور
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _usernameController,
                        decoration: const InputDecoration(
                          labelText: 'اسم المستخدم',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextFormField(
                        controller: _passwordController,
                        decoration: const InputDecoration(
                          labelText: 'كلمة المرور',
                          border: OutlineInputBorder(),
                        ),
                        obscureText:
                            false, // يمكن تغييرها إلى true لإخفاء كلمة المرور
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _macController,
                        decoration: const InputDecoration(
                          labelText: 'عنوان MAC',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextFormField(
                        controller: _routerController,
                        decoration: const InputDecoration(
                          labelText: 'اسم الراوتر',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات فنية',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 24),

                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('إلغاء'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _saveSubscriber,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(
                            context,
                          ).colorScheme.primary,
                          foregroundColor: Theme.of(
                            context,
                          ).colorScheme.onPrimary,
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : const Text('حفظ'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
