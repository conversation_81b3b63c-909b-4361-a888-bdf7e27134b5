/// إعداد<PERSON><PERSON> ZainCash للتطبيق
///
/// ⚠️ تحذير أمني: هذا الملف يحتوي على بيانات اعتماد حقيقية
/// لا تشارك هذا الملف أو تحمله على مستودعات عامة
///
class ZainCashConfig {
  // إعدادات البيئة - مضبوطة على الإنتاج
  static const bool isProduction = true; // مضبوطة على الإنتاج ✅
  
  // URLs
  static const String testBaseUrl = 'https://test.zaincash.iq';
  static const String liveBaseUrl = 'https://api.zaincash.iq';
  
  static String get baseUrl => isProduction ? liveBaseUrl : testBaseUrl;
  static String get initUrl => '$baseUrl/transaction/init';
  static String get getUrl => '$baseUrl/transaction/get';
  static String get payUrl => '$baseUrl/transaction/pay';
  
  // بيانات الإنتاج - بياناتك الحقيقية
  static const String liveMerchantId = '5eba52ff3924b2df06877ddc';
  static const String liveSecret = r'$2y$10$dozRcLK6VKjL7L19uXhVdeH7hP/RF65vNeL9w/tC/Am073TaBBwey';
  static const String liveMsisdn = '9647819597948';
  
  // بيانات الاختبار
  static const String testMerchantId = '5ffacf6612b5777c6d44266f';
  static const String testSecret = r'$2y$10$hBbAZo2GfSSvyqAyV2SaqOfYewgYpfR1O19gIh4SqyGWdmySZYPuS';
  static const String testMsisdn = '9647835077893';
  
  // بيانات العميل للاختبار
  static const String testCustomerMsisdn = '9647802999569';
  static const String testCustomerPin = '1234';
  static const String testCustomerOtp = '1111';

  // بيانات العميل للإنتاج (يجب أن تكون محفظة حقيقية مسجلة)
  static const String productionCustomerMsisdn = '9647806999267'; // رقم من بيانات الاختبار الرسمية
  static const String productionCustomerPin = '6847'; // PIN من بيانات الاختبار الرسمية
  static const String productionCustomerOtp = '1111'; // OTP للاختبار
  
  // الحصول على البيانات الحالية
  static String get merchantId => isProduction ? liveMerchantId : testMerchantId;
  static String get secret => isProduction ? liveSecret : testSecret;
  static String get msisdn => isProduction ? liveMsisdn : testMsisdn;

  // الحصول على بيانات العميل حسب البيئة
  static String get customerMsisdn => isProduction ? productionCustomerMsisdn : testCustomerMsisdn;
  static String get customerPin => isProduction ? productionCustomerPin : testCustomerPin;
  static String get customerOtp => isProduction ? productionCustomerOtp : testCustomerOtp;
  
  // إعدادات عامة
  static const int minAmount = 250; // الحد الأدنى بالدينار العراقي (حسب وثائق ZainCash الرسمية)
  static const int tokenExpiryHours = 4; // انتهاء صلاحية التوكن
  static const String defaultLanguage = 'ar';
  // صفحة استقبال نتائج الدفع على GitHub Pages - محدثة لدعم OTP
  static const String redirectUrl = 'https://ahmedha43.github.io/isp-manager-payment-success/';

  // رابط مباشر للملف على GitHub (بديل مؤقت)
  static const String rawGitHubUrl = 'https://raw.githubusercontent.com/ahmedha43/isp-manager-payment-success/main/index.html';

  // للاختبار المحلي، يمكن استخدام httpbin.org مؤقتاً
  static const String testRedirectUrl = 'https://httpbin.org/get';
  
  // رسائل الخطأ - محدثة لدعم OTP
  static const Map<String, String> errorMessages = {
    'min_amount': 'المبلغ يجب أن يكون 250 دينار عراقي على الأقل',
    'invalid_credentials': 'بيانات الاعتماد غير صحيحة',
    'connection_error': 'خطأ في الاتصال بخدمة ZainCash',
    'invalid_token': 'رمز المصادقة غير صحيح',
    'transaction_not_found': 'لم يتم العثور على المعاملة',
    'payment_failed': 'فشل في عملية الدفع',
    'payment_pending': 'الدفع في انتظار التأكيد',
    'payment_cancelled': 'تم إلغاء عملية الدفع',
    'invalid_phone': 'رقم الهاتف غير صحيح',
    'invalid_pin': 'الرقم السري غير صحيح',
    'invalid_otp': 'رمز التحقق OTP غير صحيح',
    'otp_expired': 'انتهت صلاحية رمز التحقق',
    'insufficient_balance': 'رصيد المحفظة غير كافي',
    'transaction_timeout': 'انتهت مهلة المعاملة',
  };
  
  // حالات المعاملة - محدثة لدعم OTP
  static const Map<String, String> transactionStatuses = {
    'pending': 'في الانتظار',
    'pending_otp': 'في انتظار رمز التحقق',
    'processing': 'قيد المعالجة',
    'success': 'نجح',
    'completed': 'مكتمل',
    'failed': 'فشل',
    'cancelled': 'ملغي',
    'expired': 'منتهي الصلاحية',
  };
  
  /// التحقق من صحة الإعدادات
  static bool validateConfig() {
    if (merchantId.isEmpty || secret.isEmpty || msisdn.isEmpty) {
      print('خطأ: بيانات الاعتماد غير مكتملة');
      return false;
    }

    // التحقق من أن البيانات الحقيقية مُكوّنة
    if (isProduction) {
      if (merchantId == liveMerchantId &&
          secret == liveSecret &&
          msisdn == liveMsisdn) {
        print('✅ تم تكوين بيانات الإنتاج الحقيقية بنجاح');
      } else {
        print('⚠️ تحذير: بيانات الإنتاج غير متطابقة');
      }
    }

    if (redirectUrl.contains('your-app.com')) {
      print('تحذير: يُنصح بتحديث redirectUrl ليشير لدومين تطبيقك الحقيقي');
    }

    return true;
  }
  
  /// الحصول على معلومات البيئة الحالية
  static Map<String, dynamic> getEnvironmentInfo() {
    return {
      'environment': isProduction ? 'production' : 'test',
      'baseUrl': baseUrl,
      'merchantId': merchantId,
      'msisdn': msisdn,
      'minAmount': minAmount,
      'tokenExpiryHours': tokenExpiryHours,
    };
  }
  
  /// تبديل البيئة (للتطوير فقط)
  static void switchEnvironment() {
    // هذه الدالة للتطوير فقط
    // في الإنتاج، يجب تغيير isProduction مباشرة في الكود
    print('Current environment: ${isProduction ? 'production' : 'test'}');
    print('To switch environment, change isProduction value in ZainCashConfig');
    print('');
    print('🔧 للتطوير والاختبار:');
    print('   isProduction = false');
    print('   يستخدم: test.zaincash.iq');
    print('   بيانات الاختبار: ${testCustomerMsisdn} / ${testCustomerPin} / ${testCustomerOtp}');
    print('');
    print('🚀 للإنتاج:');
    print('   isProduction = true');
    print('   يستخدم: api.zaincash.iq');
    print('   بيانات حقيقية للعملاء');
  }
}
