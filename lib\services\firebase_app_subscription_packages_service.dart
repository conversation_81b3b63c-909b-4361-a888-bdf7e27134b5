import 'package:cloud_firestore/cloud_firestore.dart';

class FirebaseAppSubscriptionPackagesService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// جلب جميع باقات اشتراكات التطبيق النشطة
  Future<List<Map<String, dynamic>>> getActiveAppSubscriptionPackages() async {
    try {
      final querySnapshot = await _firestore
          .collection('app_subscription_packages')
          .where('is_active', isEqualTo: true)
          .orderBy('price')
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('خطأ في جلب باقات اشتراكات التطبيق: $e');
      return [];
    }
  }

  /// جلب جميع باقات اشتراكات التطبيق
  Future<List<Map<String, dynamic>>> getAllAppSubscriptionPackages() async {
    try {
      final querySnapshot = await _firestore
          .collection('app_subscription_packages')
          .orderBy('created_at', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('خطأ في جلب جميع باقات اشتراكات التطبيق: $e');
      return [];
    }
  }

  /// جلب باقة اشتراك تطبيق محددة بالمعرف
  Future<Map<String, dynamic>?> getAppSubscriptionPackageById(String packageId) async {
    try {
      final doc = await _firestore
          .collection('app_subscription_packages')
          .doc(packageId)
          .get();

      if (doc.exists) {
        final data = doc.data()!;
        data['id'] = doc.id;
        return data;
      }
      return null;
    } catch (e) {
      print('خطأ في جلب باقة اشتراك التطبيق: $e');
      return null;
    }
  }

  /// إنشاء باقة اشتراك تطبيق جديدة
  Future<String> createAppSubscriptionPackage(Map<String, dynamic> packageData) async {
    try {
      final docRef = await _firestore.collection('app_subscription_packages').add({
        ...packageData,
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
      });
      return docRef.id;
    } catch (e) {
      print('خطأ في إنشاء باقة اشتراك التطبيق: $e');
      rethrow;
    }
  }

  /// تحديث باقة اشتراك تطبيق موجودة
  Future<void> updateAppSubscriptionPackage(String packageId, Map<String, dynamic> packageData) async {
    try {
      await _firestore
          .collection('app_subscription_packages')
          .doc(packageId)
          .update({
        ...packageData,
        'updated_at': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('خطأ في تحديث باقة اشتراك التطبيق: $e');
      rethrow;
    }
  }

  /// حذف باقة اشتراك تطبيق
  Future<void> deleteAppSubscriptionPackage(String packageId) async {
    try {
      await _firestore
          .collection('app_subscription_packages')
          .doc(packageId)
          .delete();
    } catch (e) {
      print('خطأ في حذف باقة اشتراك التطبيق: $e');
      rethrow;
    }
  }

  /// جلب أرقام الواتساب النشطة
  Future<List<Map<String, dynamic>>> getActiveWhatsAppNumbers() async {
    try {
      final querySnapshot = await _firestore
          .collection('whatsapp_numbers')
          .where('is_active', isEqualTo: true)
          .orderBy('created_at', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('خطأ في جلب أرقام الواتساب: $e');
      return [];
    }
  }

  /// جلب أول رقم واتساب نشط
  Future<String?> getFirstActiveWhatsAppNumber() async {
    try {
      final querySnapshot = await _firestore
          .collection('whatsapp_numbers')
          .where('is_active', isEqualTo: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return querySnapshot.docs.first.data()['number'] as String?;
      }
      return null;
    } catch (e) {
      print('خطأ في جلب رقم الواتساب: $e');
      return null;
    }
  }

  /// إنشاء رقم واتساب جديد
  Future<String> createWhatsAppNumber(Map<String, dynamic> numberData) async {
    try {
      final docRef = await _firestore.collection('whatsapp_numbers').add({
        ...numberData,
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
      });
      return docRef.id;
    } catch (e) {
      print('خطأ في إنشاء رقم الواتساب: $e');
      rethrow;
    }
  }

  /// تحديث رقم واتساب
  Future<void> updateWhatsAppNumber(String numberId, Map<String, dynamic> numberData) async {
    try {
      await _firestore
          .collection('whatsapp_numbers')
          .doc(numberId)
          .update({
        ...numberData,
        'updated_at': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('خطأ في تحديث رقم الواتساب: $e');
      rethrow;
    }
  }

  /// حذف رقم واتساب
  Future<void> deleteWhatsAppNumber(String numberId) async {
    try {
      await _firestore
          .collection('whatsapp_numbers')
          .doc(numberId)
          .delete();
    } catch (e) {
      print('خطأ في حذف رقم الواتساب: $e');
      rethrow;
    }
  }

  /// جلب باقات اشتراكات التطبيق الموصى بها
  Future<List<Map<String, dynamic>>> getRecommendedAppSubscriptionPackages() async {
    try {
      final querySnapshot = await _firestore
          .collection('app_subscription_packages')
          .where('is_active', isEqualTo: true)
          .where('is_recommended', isEqualTo: true)
          .orderBy('price')
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('خطأ في جلب باقات اشتراكات التطبيق الموصى بها: $e');
      return [];
    }
  }

  /// إنشاء باقات افتراضية لاشتراكات التطبيق
  Future<void> createDefaultAppSubscriptionPackages() async {
    try {
      final defaultPackages = [
        {
          'name': 'اشتراك تجريبي',
          'price': 0,
          'duration_days': 7,
          'details': 'فترة تجريبية مجانية لمدة أسبوع',
          'is_active': true,
          'is_recommended': false,
          'features': ['الوصول الكامل للتطبيق', 'دعم فني محدود'],
        },
        {
          'name': 'اشتراك شهري',
          'price': 50000,
          'duration_days': 30,
          'details': 'اشتراك شهري كامل مع جميع المميزات',
          'is_active': true,
          'is_recommended': true,
          'features': ['الوصول الكامل للتطبيق', 'دعم فني كامل', 'تحديثات مجانية'],
        },
        {
          'name': 'اشتراك ربع سنوي',
          'price': 120000,
          'duration_days': 90,
          'details': 'اشتراك لمدة 3 أشهر مع خصم 20%',
          'is_active': true,
          'is_recommended': false,
          'features': ['الوصول الكامل للتطبيق', 'دعم فني كامل', 'تحديثات مجانية', 'خصم 20%'],
        },
        {
          'name': 'اشتراك سنوي',
          'price': 400000,
          'duration_days': 365,
          'details': 'اشتراك سنوي كامل مع خصم 33%',
          'is_active': true,
          'is_recommended': false,
          'features': ['الوصول الكامل للتطبيق', 'دعم فني كامل', 'تحديثات مجانية', 'خصم 33%', 'أولوية في الدعم'],
        },
      ];

      for (final package in defaultPackages) {
        await createAppSubscriptionPackage(package);
      }

      print('✅ تم إنشاء باقات اشتراكات التطبيق الافتراضية');
    } catch (e) {
      print('خطأ في إنشاء باقات اشتراكات التطبيق الافتراضية: $e');
    }
  }

  /// التأكد فقط من وجود الباقات الافتراضية (شهر، 6 أشهر، سنة) وإنشاؤها إذا لم تكن موجودة، دون حذف أي باقة موجودة
  Future<void> ensureDefaultAppSubscriptionPackagesExist() async {
    final packagesRef = _firestore.collection('app_subscription_packages');

    // تعريف الباقات الافتراضية المحسنة
    final List<Map<String, dynamic>> defaultPackages = [
      {
        'name': 'الباقة الشهرية',
        'duration_days': 30,
        'price': 5000,
        'is_active': true,
        'is_recommended': true,
        'details': 'أفضل خيار للتجربة أو الاستخدام القصير.',
        'features': [
          'دعم فني سريع',
          'تحديثات مجانية',
          'إمكانية الترقية في أي وقت',
        ],
      },
      {
        'name': 'الباقة نصف السنوية',
        'duration_days': 180,
        'price': 25000,
        'is_active': true,
        'is_recommended': false,
        'details': 'وفّر أكثر مع اشتراك 6 أشهر.',
        'features': [
          'دعم فني مميز',
          'تحديثات مجانية',
          'خصم خاص',
        ],
      },
      {
        'name': 'الباقة السنوية',
        'duration_days': 365,
        'price': 45000,
        'is_active': true,
        'is_recommended': false,
        'details': 'راحة بال لعام كامل مع أفضل سعر.',
        'features': [
          'دعم فني VIP',
          'تحديثات مجانية طوال السنة',
          'أفضل قيمة مقابل السعر',
        ],
      },
    ];

    // جلب جميع الباقات الحالية
    final allPackagesSnap = await packagesRef.get();
    final List<Map<String, dynamic>> allPackages = allPackagesSnap.docs.map((doc) {
      final data = doc.data();
      data['id'] = doc.id;
      return data;
    }).toList();

    // إنشاء الباقات الافتراضية إذا لم تكن موجودة
    for (var def in defaultPackages) {
      final exists = allPackages.any((p) =>
        (p['name'] == def['name'] && p['duration_days'] == def['duration_days'])
      );
      if (!exists) {
        await packagesRef.add({
          ...def,
          'created_at': FieldValue.serverTimestamp(),
          'updated_at': FieldValue.serverTimestamp(),
        });
      }
    }
  }

  /// تفعيل اشتراك جديد للمستخدم
  Future<Map<String, dynamic>> activateSubscription({
    required String accountNumber,
    required int durationDays,
    required String packageName,
    required String paymentMethod,
    required String transactionId,
  }) async {
    try {
      final subscriptionsRef = _firestore.collection('device_subscriptions');

      // البحث عن الاشتراك الحالي
      final existingSubscriptionQuery = await subscriptionsRef
          .where('account_number', isEqualTo: accountNumber)
          .limit(1)
          .get();

      final now = DateTime.now();
      DateTime newEndDate;

      if (existingSubscriptionQuery.docs.isNotEmpty) {
        // تمديد الاشتراك الموجود
        final existingDoc = existingSubscriptionQuery.docs.first;
        final existingData = existingDoc.data();
        final currentEndDate = (existingData['end_date'] as Timestamp).toDate();

        // إذا كان الاشتراك منتهي، ابدأ من الآن، وإلا أضف المدة للتاريخ الحالي
        if (currentEndDate.isBefore(now)) {
          newEndDate = now.add(Duration(days: durationDays));
        } else {
          newEndDate = currentEndDate.add(Duration(days: durationDays));
        }

        // تحديث الاشتراك الموجود
        await existingDoc.reference.update({
          'end_date': Timestamp.fromDate(newEndDate),
          'is_active': true,
          'last_payment_method': paymentMethod,
          'last_transaction_id': transactionId,
          'last_package_name': packageName,
          'updated_at': FieldValue.serverTimestamp(),
        });

        return {
          'success': true,
          'subscription': {
            'account_number': accountNumber,
            'end_date': newEndDate.toIso8601String(),
            'is_active': true,
            'package_name': packageName,
          },
        };
      } else {
        // إنشاء اشتراك جديد
        newEndDate = now.add(Duration(days: durationDays));

        await subscriptionsRef.add({
          'account_number': accountNumber,
          'start_date': Timestamp.fromDate(now),
          'end_date': Timestamp.fromDate(newEndDate),
          'is_active': true,
          'payment_method': paymentMethod,
          'transaction_id': transactionId,
          'package_name': packageName,
          'created_at': FieldValue.serverTimestamp(),
          'updated_at': FieldValue.serverTimestamp(),
        });

        return {
          'success': true,
          'subscription': {
            'account_number': accountNumber,
            'end_date': newEndDate.toIso8601String(),
            'is_active': true,
            'package_name': packageName,
          },
        };
      }
    } catch (e) {
      print('خطأ في تفعيل الاشتراك: $e');
      return {
        'success': false,
        'error': 'فشل في تفعيل الاشتراك: $e',
      };
    }
  }
}