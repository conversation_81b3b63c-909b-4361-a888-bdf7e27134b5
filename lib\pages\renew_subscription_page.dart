import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:android_intent_plus/android_intent.dart';
import 'package:android_intent_plus/flag.dart';

import '../models/device_subscription_model.dart';
import '../services/firebase_app_subscription_packages_service.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/firebase_app_settings_service.dart';
import '../services/zaincash_service.dart';
import '../config/zaincash_config.dart';
import 'payment_history_page.dart';
import 'zaincash_test_page.dart';
import 'zaincash_otp_payment_page.dart';

class RenewSubscriptionPage extends StatefulWidget {
  final DeviceSubscription deviceSubscription;
  const RenewSubscriptionPage({super.key, required this.deviceSubscription});

  @override
  State<RenewSubscriptionPage> createState() => _RenewSubscriptionPageState();
}

class _RenewSubscriptionPageState extends State<RenewSubscriptionPage> {
  List<Map<String, dynamic>> _packages = [];
  String? _whatsappNumber;
  bool _isLoading = true;
  String? _error;
  String? _selectedPackageId;
  final FirebaseAppSubscriptionPackagesService _packagesService = FirebaseAppSubscriptionPackagesService();
  final ZainCashService _zainCashService = ZainCashService();


  @override
  void initState() {
    super.initState();
    _fetchData();
  }





  Future<void> _fetchData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });
    try {
      // تنظيف وإنشاء الباقات الافتراضية إذا لم توجد
      await _packagesService.ensureDefaultAppSubscriptionPackagesExist();
      final packagesRes = await _packagesService.getActiveAppSubscriptionPackages();
      final whatsappNumber = await _packagesService.getFirstActiveWhatsAppNumber();

      if (mounted) {
        setState(() {
          _packages = packagesRes;
          _whatsappNumber = whatsappNumber;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'خطأ في تحميل الباقات: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _openWhatsapp(Map<String, dynamic> package) async {
    if (_whatsappNumber == null) {
      // إنشاء بيانات افتراضية إذا لم تكن موجودة
      await FirebaseAppSettingsService.ensureDefaultWhatsAppNumber();
      // رابط إنشاء فهرس whatsapp_numbers في Firebase Console
      const indexUrl = 'https://console.firebase.google.com/project/YOUR_PROJECT_ID/firestore/indexes?create_composite=CUSTOM_INDEX_ID';
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('رقم الواتساب غير متوفر'),
            content: Text('رقم الواتساب غير متوفر في قاعدة البيانات. يمكنك إنشاء فهرس لجدول أرقام الواتساب من هنا:'),
            actions: [
              TextButton(
                onPressed: () async {
                  if (await canLaunchUrl(Uri.parse(indexUrl))) {
                    await launchUrl(Uri.parse(indexUrl));
                  }
                },
                child: Text('فتح صفحة الفهارس'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إغلاق'),
              ),
            ],
          ),
        );
      }
      return;
    }

    final cleanWhatsAppNumber = _whatsappNumber!.replaceAll(RegExp(r'[^ -9+]'), '');
    final msg =
        'مرحباً، أود شراء باقة اشتراك تطبيق\n\n'
        '**رقم الحساب:** ${widget.deviceSubscription.accountNumber}\n'
        '**الباقة المختارة:** ${package['name']}\n'
        '**السعر:** ${package['price']} د.ع\n'
        '**المدة:** ${package['duration_days']} يوم\n\n'
        'الرجاء تزويدي بتعليمات الدفع.';

    final uri = Uri.parse('https://wa.me/$cleanWhatsAppNumber?text=${Uri.encodeComponent(msg)}');

    try {
      final intent = AndroidIntent(
        action: 'action_view',
        data: uri.toString(),
        package: 'com.whatsapp',
        flags: <int>[Flag.FLAG_ACTIVITY_NEW_TASK],
      );
      await intent.launch();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم فتح واتساب بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      _showErrorSnackBar('تعذر فتح واتساب عبر android_intent_plus: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return WillPopScope(
      onWillPop: () async => false, // يمنع الرجوع للخلف
      child: Scaffold(
        appBar: AppBar(
          title: const Text('باقات اشتراك التطبيق', style: TextStyle(fontWeight: FontWeight.bold)),
          backgroundColor: theme.colorScheme.surface,
          elevation: 1,
          actions: [
            IconButton(
              icon: const Icon(Icons.history),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PaymentHistoryPage(),
                  ),
                );
              },
              tooltip: 'تاريخ المدفوعات',
            ),
            IconButton(
              icon: const Icon(Icons.bug_report),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ZainCashTestPage(),
                  ),
                );
              },
              tooltip: 'اختبار ZainCash',
            ),
          ],
        ),
        body: _buildBody(theme),
      ),
    );
  }

  Widget _buildBody(ThemeData theme) {
    if (_isLoading) {
      return _buildLoadingShimmer();
    }
    if (_error != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, color: theme.colorScheme.error, size: 50),
              const SizedBox(height: 16),
              Text(_error!, style: theme.textTheme.titleMedium, textAlign: TextAlign.center),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: _fetchData,
                icon: const Icon(Icons.refresh),
                label: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }
    return RefreshIndicator(
      onRefresh: _fetchData,
      child: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildAccountHeader(theme),
          const SizedBox(height: 16),
          _buildHeader(theme),
          const SizedBox(height: 24),
          if (_packages.isEmpty)
            _buildEmptyPackages(theme)
          else ..._packages.map((pkg) => _buildPackageCard(pkg, theme)),
        ],
      ),
    );
  }

  Widget _buildAccountHeader(ThemeData theme) {
    final sub = widget.deviceSubscription;
    return Card(
      elevation: 3,
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(Icons.account_circle, color: theme.colorScheme.primary, size: 32),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('رقم الحساب: ${sub.accountNumber}', style: theme.textTheme.titleMedium),
                  Row(
                    children: [
                      Icon(sub.isActive ? Icons.check_circle : Icons.cancel, color: sub.isActive ? Colors.green : Colors.red, size: 18),
                      const SizedBox(width: 4),
                      Text('الحالة: ', style: theme.textTheme.bodyMedium),
                      Text(sub.isActive ? 'نشط' : 'منتهي', style: TextStyle(color: sub.isActive ? Colors.green : Colors.red, fontWeight: FontWeight.bold)),
                    ],
                  ),
                  Text('ينتهي في: ${sub.formattedEndDate}', style: theme.textTheme.bodySmall),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'باقات اشتراك التطبيق',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اختر باقة اشتراك التطبيق التي تناسبك وقم بتفعيلها مباشرة عبر واتساب.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyPackages(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.subscriptions, color: theme.colorScheme.primary.withOpacity(0.7), size: 64),
            const SizedBox(height: 16),
            Text(
              'لا توجد باقات اشتراك تطبيق متاحة',
              style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'سيتم إنشاء الباقات الافتراضية تلقائياً عند الحاجة',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _fetchData,
              icon: const Icon(Icons.refresh),
              label: const Text('تحديث'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPackageCard(Map<String, dynamic> package, ThemeData theme) {
    final isSelected = _selectedPackageId == package['id'];
    final isRecommended = package['is_recommended'] == true;

    return AnimatedContainer(
      duration: Duration(milliseconds: 250),
      curve: Curves.easeInOut,
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: isSelected
            ? theme.colorScheme.primary.withOpacity(0.13)
            : theme.cardColor,
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: isSelected ? theme.colorScheme.primary : (isRecommended ? theme.colorScheme.secondary : Colors.transparent),
          width: isSelected || isRecommended ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isSelected
                ? theme.colorScheme.primary.withOpacity(0.18)
                : Colors.black.withOpacity(0.07),
            blurRadius: isSelected ? 18 : 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(18),
        onTap: () {
          setState(() => _selectedPackageId = package['id']);
        },
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      package['name'],
                      style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                    ),
                  ),
                  if (isRecommended)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.secondary,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text('الأكثر شعبية', style: TextStyle(color: Colors.white, fontSize: 12)),
                    ),
                ],
              ),
              if (package['details'] != null && package['details'].toString().isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 6, bottom: 6),
                  child: Text(
                    package['details'],
                    style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurface.withOpacity(0.7)),
                  ),
                ),
              Text(
                '${package['duration_days']} يوم - ${package['price']} د.ع',
                style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              if (package['features'] != null && package['features'] is List)
                ...((package['features'] as List).map((f) => Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green, size: 16),
                    SizedBox(width: 4),
                    Text(f.toString(), style: theme.textTheme.bodySmall),
                  ],
                ))),
              const SizedBox(height: 16),
              Row(
                children: [
                  Spacer(),
                  ElevatedButton.icon(
                    onPressed: () => _onBuyPressed(package),
                    icon: Icon(Icons.chat),
                    label: Text('شراء'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      minimumSize: Size(110, 40),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onBuyPressed(Map<String, dynamic> package) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('اختر طريقة الدفع'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('باقة: ${package['name']}'),
            Text('السعر: ${package['price']} د.ع'),
            Text('المدة: ${package['duration_days']} يوم'),
            SizedBox(height: 16),
            Text('اختر طريقة الدفع المناسبة:'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          // زين كاش - الطريقة الجديدة (داخل التطبيق)
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              _payWithZainCash(package);
            },
            icon: Icon(Icons.smartphone, color: Colors.white),
            label: Text('زين كاش (داخل التطبيق)'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
          ),
          // زين كاش - الطريقة القديمة (المتصفح) - للطوارئ
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              _showZainCashMethodChoice(package);
            },
            icon: Icon(Icons.web, color: Colors.white),
            label: Text('خيارات أخرى'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              _openWhatsapp(package);
            },
            icon: Icon(Icons.chat, color: Colors.white),
            label: Text('واتساب'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// عرض خيارات زين كاش الإضافية
  void _showZainCashMethodChoice(Map<String, dynamic> package) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.payment, color: Colors.orange),
            SizedBox(width: 8),
            Text('اختر طريقة زين كاش'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('اختر الطريقة المناسبة للدفع:'),
            SizedBox(height: 16),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[300]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'الطريقة الجديدة: داخل التطبيق مع OTP (مُوصى بها)',
                      style: TextStyle(color: Colors.green[700], fontWeight: FontWeight.bold),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 8),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange[300]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.orange[700]),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'الطريقة القديمة: عبر المتصفح (للطوارئ فقط)',
                      style: TextStyle(color: Colors.orange[700]),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              _payWithZainCash(package); // الطريقة الجديدة
            },
            icon: Icon(Icons.smartphone, color: Colors.white),
            label: Text('داخل التطبيق'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              _payWithZainCashOld(package); // الطريقة القديمة
            },
            icon: Icon(Icons.web, color: Colors.white),
            label: Text('عبر المتصفح'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// الدفع عبر ZainCash - محدث لاستخدام OTP داخل التطبيق
  Future<void> _payWithZainCash(Map<String, dynamic> package) async {
    try {
      // التحقق من صحة البيانات
      final amount = (package['price'] as num).toDouble();
      final accountNumber = widget.deviceSubscription.accountNumber;

      // التحقق من الحد الأدنى للمبلغ
      if (amount < ZainCashConfig.minAmount) {
        _showErrorSnackBar(ZainCashConfig.errorMessages['min_amount']!);
        return;
      }

      if (!_zainCashService.validatePaymentData(
        amount: amount,
        accountNumber: accountNumber,
      )) {
        _showErrorSnackBar('بيانات الدفع غير صحيحة');
        return;
      }

      // فتح صفحة الدفع مع OTP داخل التطبيق
      final result = await Navigator.of(context).push<bool>(
        MaterialPageRoute(
          builder: (context) => ZainCashOTPPaymentPage(
            packageId: package['id'],
            accountNumber: accountNumber,
            amount: amount,
            packageName: package['name'],
            durationDays: package['duration_days'],
          ),
        ),
      );

      if (result == true) {
        // تم الدفع بنجاح - إعادة تحميل البيانات
        _showSuccessSnackBar('تم الدفع وتفعيل الاشتراك بنجاح! 🎉');
        await _fetchData(); // إعادة تحميل بيانات الاشتراك
      } else if (result == false) {
        // تم إلغاء الدفع
        _showErrorSnackBar('تم إلغاء عملية الدفع');
      }
      // إذا كان result == null، فهذا يعني أن المستخدم ضغط زر الرجوع

    } catch (e) {
      _showErrorSnackBar('خطأ في عملية الدفع: $e');
    }
  }

  /// الطريقة القديمة للدفع (تفتح المتصفح) - محتفظ بها للطوارئ
  Future<void> _payWithZainCashOld(Map<String, dynamic> package) async {
    try {
      // التحقق من صحة البيانات
      final amount = (package['price'] as num).toDouble();
      final accountNumber = widget.deviceSubscription.accountNumber;

      // التحقق من الحد الأدنى للمبلغ
      if (amount < ZainCashConfig.minAmount) {
        _showErrorSnackBar(ZainCashConfig.errorMessages['min_amount']!);
        return;
      }

      if (!_zainCashService.validatePaymentData(
        amount: amount,
        accountNumber: accountNumber,
      )) {
        _showErrorSnackBar('بيانات الدفع غير صحيحة');
        return;
      }

      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري إنشاء طلب الدفع...'),
            ],
          ),
        ),
      );

      // إنشاء طلب الدفع (الطريقة القديمة)
      final paymentResult = await _zainCashService.createPaymentRequest(
        packageId: package['id'],
        accountNumber: accountNumber,
        amount: amount,
        packageName: package['name'],
        durationDays: package['duration_days'],
      );

      // إغلاق مؤشر التحميل
      if (mounted) Navigator.pop(context);

      if (paymentResult['success']) {
        // فتح رابط الدفع في المتصفح (الطريقة القديمة)
        final paymentUrl = paymentResult['paymentUrl'];
        if (await canLaunchUrl(Uri.parse(paymentUrl))) {
          await launchUrl(
            Uri.parse(paymentUrl),
            mode: LaunchMode.externalApplication,
          );

          // عرض رسالة للمستخدم مع تعليمات محدثة
          _showPaymentInstructionsUpdated(paymentResult);

          // بدء مراقبة العودة من الدفع
          _startPaymentMonitoring(paymentResult['transactionId'], paymentResult['orderId']);
        } else {
          _showErrorSnackBar('تعذر فتح رابط الدفع');
        }
      } else {
        _showErrorSnackBar(paymentResult['error'] ?? 'فشل في إنشاء طلب الدفع');
      }
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحاً
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }
      _showErrorSnackBar('خطأ في عملية الدفع: $e');
    }
  }

  /// عرض تعليمات الدفع المحدثة للمستخدم
  void _showPaymentInstructionsUpdated(Map<String, dynamic> paymentResult) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.payment, color: Colors.orange),
            SizedBox(width: 8),
            Text('تعليمات الدفع'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 20),
                  SizedBox(width: 8),
                  Expanded(child: Text('تم إنشاء طلب الدفع بنجاح!')),
                ],
              ),
            ),
            SizedBox(height: 12),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.warning, color: Colors.orange, size: 20),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '⚠️ إذا حولك ZainCash لصفحة أخرى مباشرة:',
                          style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.orange[700]),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• ارجع للتطبيق فوراً\n'
                    '• اضغط "أدخل Token يدوياً" أسفل هذه النافذة\n'
                    '• أو جرب مرة أخرى',
                    style: TextStyle(fontSize: 11, color: Colors.orange[600]),
                  ),
                ],
              ),
            ),
            SizedBox(height: 16),
            Text('رقم المعاملة:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('${paymentResult['orderId']}', style: TextStyle(fontFamily: 'monospace')),
            SizedBox(height: 16),
            Text('خطوات إكمال الدفع:', style: TextStyle(fontWeight: FontWeight.bold)),
            SizedBox(height: 8),
            _buildStep('1', 'أدخل رقم هاتف محفظة زين كاش'),
            _buildStep('2', 'أدخل رمز المحفظة (PIN)'),
            _buildStep('3', 'أدخل رمز التحقق (OTP) المرسل لهاتفك'),
            _buildStep('4', 'أدخل رمز OTP المرسل لهاتفك (مهم جداً!)'),
            _buildStep('5', 'اضغط "دفع" لإكمال العملية'),
            _buildStep('6', 'ارجع لهذا التطبيق بعد الدفع'),
            SizedBox(height: 16),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.warning, color: Colors.red, size: 20),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '⚠️ مشكلة معروفة في ZainCash:',
                          style: TextStyle(fontSize: 12, color: Colors.red[700], fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• قد يحولك ZainCash لصفحة Google مباشرة\n'
                    '• هذا يعني أن الدفع لم يكتمل\n'
                    '• ارجع للتطبيق واضغط "أدخل Token يدوياً"\n'
                    '• أو جرب مرة أخرى وأدخل البيانات بسرعة',
                    style: TextStyle(fontSize: 11, color: Colors.red[600]),
                  ),
                ],
              ),
            ),
            SizedBox(height: 12),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.blue, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'سيتم تفعيل اشتراكك تلقائياً خلال دقائق من إكمال الدفع',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('فهمت'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _checkPaymentStatus(paymentResult['transactionId'], paymentResult['orderId']);
            },
            child: Text('تحقق من الدفع الآن'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _showTokenInputDialog();
            },
            child: Text('أدخل Token يدوياً'),
          ),
        ],
      ),
    );
  }

  Widget _buildStep(String number, String text) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Center(
              child: Text(
                number,
                style: TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          SizedBox(width: 8),
          Expanded(child: Text(text, style: TextStyle(fontSize: 14))),
        ],
      ),
    );
  }

  /// عرض تعليمات الدفع للمستخدم (النسخة القديمة)
  void _showPaymentInstructions(Map<String, dynamic> paymentResult) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text('تعليمات الدفع'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تم إنشاء طلب الدفع بنجاح!'),
            SizedBox(height: 8),
            Text('رقم المعاملة: ${paymentResult['orderId']}'),
            SizedBox(height: 16),
            Text('تعليمات:'),
            Text('1. أكمل عملية الدفع في المتصفح'),
            Text('2. ارجع للتطبيق بعد إتمام الدفع'),
            Text('3. اضغط "التحقق من الدفع" للتأكد'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _checkPaymentStatus(paymentResult['transactionId'], paymentResult['orderId']);
            },
            child: Text('التحقق من الدفع'),
          ),
        ],
      ),
    );
  }

  /// التحقق من حالة الدفع
  Future<void> _checkPaymentStatus(String transactionId, String orderId) async {
    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري التحقق من الدفع...'),
            ],
          ),
        ),
      );

      // التحقق من حالة الدفع
      final statusResult = await _zainCashService.checkPaymentStatus(transactionId);

      // إغلاق مؤشر التحميل
      if (mounted) Navigator.pop(context);

      if (statusResult['success']) {
        if (statusResult['paid']) {
          // تفعيل الاشتراك
          await _activateSubscription(orderId, transactionId);
        } else {
          _showErrorSnackBar('الدفع لم يكتمل بعد. يرجى المحاولة مرة أخرى.');
        }
      } else {
        _showErrorSnackBar(statusResult['error'] ?? 'فشل في التحقق من حالة الدفع');
      }
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحاً
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }
      _showErrorSnackBar('خطأ في التحقق من الدفع: $e');
    }
  }

  /// تفعيل الاشتراك بعد نجاح الدفع
  Future<void> _activateSubscription(String orderId, String transactionId) async {
    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري تفعيل الاشتراك...'),
            ],
          ),
        ),
      );

      // تفعيل الاشتراك
      final activationResult = await _zainCashService.activateSubscription(
        orderId: orderId,
        transactionId: transactionId,
      );

      // إغلاق مؤشر التحميل
      if (mounted) Navigator.pop(context);

      if (activationResult['success']) {
        // عرض رسالة نجاح
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('تم بنجاح!'),
            content: Text('تم تفعيل اشتراكك بنجاح. يمكنك الآن الاستمتاع بجميع ميزات التطبيق.'),
            actions: [
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.pop(context); // العودة للصفحة السابقة
                },
                child: Text('موافق'),
              ),
            ],
          ),
        );
      } else {
        _showErrorSnackBar(activationResult['error'] ?? 'فشل في تفعيل الاشتراك');
      }
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحاً
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }
      _showErrorSnackBar('خطأ في تفعيل الاشتراك: $e');
    }
  }

  /// عرض نافذة إدخال Token يدوياً
  void _showTokenInputDialog() {
    final tokenController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إدخال Token يدوياً'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'إذا تم توجيهك لصفحة httpbin.org، انسخ Token من الرابط وألصقه هنا:',
              style: TextStyle(fontSize: 14),
            ),
            SizedBox(height: 16),
            TextField(
              controller: tokenController,
              decoration: InputDecoration(
                labelText: 'Token',
                hintText: 'eyJhbGciOiJIUzI1NiJ9...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              final token = tokenController.text.trim();
              if (token.isNotEmpty) {
                Navigator.pop(context);
                await _processManualToken(token);
              }
            },
            child: Text('معالجة'),
          ),
        ],
      ),
    );
  }

  /// معالجة Token المدخل يدوياً
  Future<void> _processManualToken(String token) async {
    try {
      setState(() {
        _isLoading = true;
      });

      final result = await _zainCashService.processRedirectToken(token);

      if (result['success']) {
        _showSuccessSnackBar('تم الدفع والتفعيل بنجاح!');
        await _fetchData(); // إعادة تحميل البيانات
      } else {
        _showErrorSnackBar(result['error'] ?? 'فشل في معالجة Token');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في معالجة Token: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 3),
      ),
    );
  }



  /// مراقبة حالة الدفع تلقائياً
  void _startPaymentMonitoring(String transactionId, String orderId) {
    // مراقبة كل 10 ثوان لمدة 5 دقائق
    int attempts = 0;
    const maxAttempts = 30; // 5 دقائق

    Timer.periodic(Duration(seconds: 10), (timer) async {
      attempts++;

      if (attempts > maxAttempts) {
        timer.cancel();
        return;
      }

      try {
        final statusResult = await _zainCashService.checkPaymentStatus(transactionId);

        if (statusResult['success'] && statusResult['paid']) {
          timer.cancel();

          // إظهار إشعار نجاح
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.white),
                    SizedBox(width: 8),
                    Text('تم الدفع بنجاح! جاري تفعيل الاشتراك...'),
                  ],
                ),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 3),
              ),
            );

            // تفعيل الاشتراك
            await _activateSubscription(orderId, transactionId);
          }
        }
      } catch (e) {
        // تجاهل الأخطاء في المراقبة التلقائية
        debugPrint('Payment monitoring error: $e');
      }
    });
  }

  Widget _buildLoadingShimmer() {
    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            height: 100,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
          ),
        ),
        const SizedBox(height: 24),
        ...List.generate(3, (index) => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Card(
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Container(
              height: 150,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
              ),
            ),
          ),
        )),
      ],
    );
  }
}