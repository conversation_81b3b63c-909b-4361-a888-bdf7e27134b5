import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/activation_card_model.dart';
import 'package:flutter/services.dart'; // لإدارة الحافظة
import 'package:qr_flutter/qr_flutter.dart'; // لإظهار QR Code
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:csv/csv.dart';
import 'package:uuid/uuid.dart';

class ActivationCardsManagementPage extends StatefulWidget {
  const ActivationCardsManagementPage({Key? key}) : super(key: key);

  @override
  State<ActivationCardsManagementPage> createState() => _ActivationCardsManagementPageState();
}

class _ActivationCardsManagementPageState extends State<ActivationCardsManagementPage> {
  late String adminId;
  bool _isLoading = true;
  List<ActivationCardModel> _cards = [];
  String _search = '';
  final TextEditingController _searchController = TextEditingController();
  Map<String, String> _subscriberNames = {};
  String filterStatus = 'all';
  Set<String> _selectedCards = {};

  // متغيرات التحميل التدريجي
  final ScrollController _scrollController = ScrollController();
  DocumentSnapshot? _lastDocument;
  bool _isFetchingMore = false;
  bool _hasMore = true;
  final int _perPage = 20; // عدد البطاقات في كل دفعة

  // متغيرات الإحصائيات
  int _totalCount = 0;
  int _usedCount = 0;
  int _unusedCount = 0;

  @override
  void initState() {
    super.initState();
    _loadAdminIdAndFetchInitialData();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200 &&
        !_isFetchingMore &&
        _hasMore) {
      _fetchMoreCards();
    }
  }

  Future<void> _loadAdminIdAndFetchInitialData() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;
    final userDoc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
    adminId = userDoc['adminId'];
    await _updateStats();
    await _fetchInitialCards();
  }

  Future<void> _fetchInitialCards() async {
    setState(() {
      _isLoading = true;
      _cards = [];
      _lastDocument = null;
      _hasMore = true;
    });
    await _fetchCards();
  }

  Future<void> _fetchMoreCards() async {
    if (_isFetchingMore || !_hasMore) return;
    setState(() => _isFetchingMore = true);
    await _fetchCards();
  }

  Future<void> _fetchCards() async {
    Query query = FirebaseFirestore.instance
        .collection('activation_cards')
        .where('adminId', isEqualTo: adminId);

    // تطبيق الفلترة بالحالة
    if (filterStatus == 'used') {
      query = query.where('isUsed', isEqualTo: true);
    } else if (filterStatus == 'unused') {
      query = query.where('isUsed', isEqualTo: false);
    }

    // البحث الأساسي
    if (_search.isNotEmpty) {
      query = query.where('id', isGreaterThanOrEqualTo: _search)
                   .where('id', isLessThanOrEqualTo: _search + '\uf8ff');
    }

    query = query.orderBy('createdAt', descending: true);

    if (_lastDocument != null) {
      query = query.startAfterDocument(_lastDocument!);
    }

    final snapshot = await query.limit(_perPage).get();

    if (snapshot.docs.length < _perPage) {
      _hasMore = false;
    }

    if (snapshot.docs.isNotEmpty) {
      _lastDocument = snapshot.docs.last;
      final newCards = snapshot.docs.map((doc) => ActivationCardModel.fromMap(doc.data() as Map<String, dynamic>)).toList();
      setState(() {
        _cards.addAll(newCards);
      });
      await _fetchSubscriberNamesForCards(newCards);
    }

    setState(() {
      _isLoading = false;
      _isFetchingMore = false;
    });
  }

  Future<void> _fetchSubscriberNamesForCards(List<ActivationCardModel> cards) async {
    final usedIds = cards.where((c) => c.isUsed && c.usedBy != null).map((c) => c.usedBy!).toSet();
    if (usedIds.isEmpty) return;
    // جلب الأسماء التي لم يتم جلبها من قبل فقط
    final idsToFetch = usedIds.where((id) => !_subscriberNames.containsKey(id)).toList();
    if (idsToFetch.isEmpty) return;
    // قسم idsToFetch إلى دفعات كل دفعة 10
    for (int i = 0; i < idsToFetch.length; i += 10) {
      final batchIds = idsToFetch.sublist(i, (i + 10 > idsToFetch.length) ? idsToFetch.length : i + 10);
      final subsSnapshot = await FirebaseFirestore.instance
          .collection('subscribers')
          .where('id', whereIn: batchIds)
          .get();
      for (var doc in subsSnapshot.docs) {
        _subscriberNames[doc['id']] = doc['fullName'] ?? '';
      }
    }
    setState(() {});
  }

  Future<void> _updateStats() async {
    final collection = FirebaseFirestore.instance.collection('activation_cards').where('adminId', isEqualTo: adminId);
    final totalQuery = collection.count();
    final usedQuery = collection.where('isUsed', isEqualTo: true).count();
    final totalResult = await totalQuery.get();
    final usedResult = await usedQuery.get();
    setState(() {
      _totalCount = totalResult.count ?? 0;
      _usedCount = usedResult.count ?? 0;
      _unusedCount = _totalCount - _usedCount;
    });
  }

  Future<void> _addCardDialog() async {
    final formKey = GlobalKey<FormState>();
    final codeController = TextEditingController();
    final valueController = TextEditingController();
    bool autoGenerate = true;
    await showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              title: const Text('إضافة بطاقة تفعيل'),
              content: Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Checkbox(
                          value: autoGenerate,
                          onChanged: (v) => setState(() => autoGenerate = v ?? true),
                        ),
                        const Text('توليد الكود تلقائياً'),
                      ],
                    ),
                    if (!autoGenerate)
                      TextFormField(
                        controller: codeController,
                        decoration: const InputDecoration(labelText: 'كود البطاقة'),
                        validator: (v) => v == null || v.isEmpty ? 'أدخل كود البطاقة' : null,
                      ),
                    TextFormField(
                      controller: valueController,
                      decoration: const InputDecoration(labelText: 'القيمة (عدد الأيام أو اسم الباقة)'),
                      validator: (v) => v == null || v.isEmpty ? 'أدخل قيمة البطاقة' : null,
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    if (!formKey.currentState!.validate()) return;
                    final code = autoGenerate ? _generateCode() : codeController.text.trim();
                    final value = valueController.text.trim();
                    final card = ActivationCardModel(
                      id: code,
                      adminId: adminId,
                      value: value,
                      isUsed: false,
                      usedBy: null,
                      createdAt: DateTime.now(),
                      usedAt: null,
                    );
                    await FirebaseFirestore.instance
                        .collection('activation_cards')
                        .doc(card.id)
                        .set(card.toMap());
                    Navigator.of(context).pop();
                    _fetchInitialCards(); // تحديث
                    _updateStats(); // تحديث
                  },
                  child: const Text('إضافة'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  String _generateCode() {
    const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
    return List.generate(8, (i) => chars[(DateTime.now().millisecondsSinceEpoch + i * 13) % chars.length]).join();
  }

  Future<void> _generateBulkDialog() async {
    final formKey = GlobalKey<FormState>();
    final valueController = TextEditingController();
    final countController = TextEditingController(text: '10');
    bool isLoading = false;
    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: const Text('توليد عدة بطاقات'),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: valueController,
                  decoration: const InputDecoration(labelText: 'القيمة (عدد الأيام أو اسم الباقة)'),
                  validator: (v) => v == null || v.isEmpty ? 'أدخل قيمة البطاقة' : null,
                ),
                TextFormField(
                  controller: countController,
                  decoration: const InputDecoration(labelText: 'عدد البطاقات'),
                  keyboardType: TextInputType.number,
                  validator: (v) {
                    final n = int.tryParse(v ?? '');
                    if (n == null || n < 1 || n > 10000) return 'أدخل رقم بين 1 و 10000';
                    return null;
                  },
                ),
                if (isLoading) const Padding(
                  padding: EdgeInsets.all(12),
                  child: CircularProgressIndicator(),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: isLoading ? null : () async {
                if (!formKey.currentState!.validate()) return;
                setState(() => isLoading = true);
                final value = valueController.text.trim();
                final count = int.parse(countController.text.trim());
                final now = DateTime.now();
                final batchSize = 500;
                final uuid = Uuid();
                try {
                  Set<String> generatedCodes = {};
                  for (int i = 0; i < count; i += batchSize) {
                    final batch = FirebaseFirestore.instance.batch();
                    final end = (i + batchSize > count) ? count : i + batchSize;
                    for (int j = i; j < end; j++) {
                      String code;
                      do {
                        code = uuid.v4().substring(0, 8).toUpperCase();
                      } while (generatedCodes.contains(code));
                      generatedCodes.add(code);
                      final card = ActivationCardModel(
                        id: code,
                        adminId: adminId,
                        value: value,
                        isUsed: false,
                        usedBy: null,
                        createdAt: now,
                        usedAt: null,
                      );
                      final doc = FirebaseFirestore.instance.collection('activation_cards').doc(card.id);
                      batch.set(doc, card.toMap());
                    }
                    await batch.commit();
                  }
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('تم توليد $count بطاقة بنجاح!')),
                  );
                  _fetchInitialCards();
                  _updateStats();
                } catch (e) {
                  setState(() => isLoading = false);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('حدث خطأ أثناء التوليد: $e')),
                  );
                }
              },
              child: const Text('توليد'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _exportUnusedCards() async {
    final unused = _cards.where((c) => !c.isUsed).toList();
    if (unused.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('لا توجد بطاقات غير مستخدمة للتصدير.')));
      return;
    }
    final text = unused.map((c) => c.id).join('\n');
    await Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم نسخ الأكواد غير المستخدمة إلى الحافظة.')));
    // يمكن لاحقاً إضافة تصدير ملف نصي إذا رغبت
  }

  Future<void> _deleteCard(ActivationCardModel card) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('تأكيد الحذف'),
        content: Text('هل تريد حذف بطاقة التفعيل (${card.id})؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
    if (confirm == true) {
      await FirebaseFirestore.instance.collection('activation_cards').doc(card.id).delete();
      _fetchInitialCards(); // تحديث
      _updateStats(); // تحديث
    }
  }

  @override
  Widget build(BuildContext context) {
    final total = _totalCount;
    final used = _usedCount;
    final unused = _unusedCount;
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة بطاقات التفعيل'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addCardDialog,
            tooltip: 'إضافة بطاقة',
          ),
          IconButton(
            icon: const Icon(Icons.library_add),
            onPressed: _generateBulkDialog,
            tooltip: 'توليد عدة بطاقات',
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: _exportUnusedCards,
            tooltip: 'تصدير البطاقات غير المستخدمة',
          ),
        ],
      ),
      body: Stack(
        children: [
          Column(
            children: [
              // شريط الإحصائيات
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _StatCard(label: 'الإجمالي', value: total, color: Colors.blue),
                    _StatCard(label: 'غير مستخدمة', value: unused, color: Colors.green),
                    _StatCard(label: 'مستخدمة', value: used, color: Colors.grey),
                  ],
                ),
              ),
              // البحث والفلترة
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _searchController,
                        decoration: const InputDecoration(
                          prefixIcon: Icon(Icons.search),
                          hintText: 'بحث عن كود أو قيمة البطاقة...'
                        ),
                        onChanged: (v) => setState(() => _search = v.trim()),
                      ),
                    ),
                    const SizedBox(width: 8),
                    DropdownButton<String>(
                      value: filterStatus,
                      items: const [
                        DropdownMenuItem(value: 'all', child: Text('الكل')),
                        DropdownMenuItem(value: 'unused', child: Text('غير مستخدمة')),
                        DropdownMenuItem(value: 'used', child: Text('مستخدمة')),
                      ],
                      onChanged: (v) => setState(() => filterStatus = v ?? 'all'),
                      underline: Container(),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton.icon(
                      icon: Icon(_isAllSelected() ? Icons.clear : Icons.select_all),
                      label: Text(_isAllSelected() ? 'إلغاء الكل' : 'تحديد الكل'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue.shade50,
                        foregroundColor: Colors.blue,
                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                        textStyle: const TextStyle(fontSize: 13),
                        elevation: 0,
                      ),
                      onPressed: _showSelectAllDialog,
                    ),
                  ],
                ),
              ),
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _cards.isEmpty
                        ? const Center(child: Text('لا توجد بطاقات تفعيل بعد.'))
                        : ListView.separated(
                            controller: _scrollController, // ربط الـ controller
                            padding: const EdgeInsets.all(16),
                            itemCount: _cards.length + (_hasMore ? 1 : 0), // إضافة عنصر لمؤشر التحميل
                            separatorBuilder: (_, __) => const SizedBox(height: 12),
                            itemBuilder: (context, i) {
                              if (i == _cards.length) {
                                return const Center(child: CircularProgressIndicator());
                              }
                              final card = _cards[i];
                              final isSelected = _selectedCards.contains(card.id);
                              return GestureDetector(
                                onTap: () => _showCardDetails(card),
                                child: Card(
                                  elevation: 3,
                                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                  color: card.isUsed ? Colors.grey[200] : Colors.green[50],
                                  child: ListTile(
                                    leading: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Checkbox(
                                          value: isSelected,
                                          onChanged: (v) {
                                            setState(() {
                                              if (v == true) {
                                                _selectedCards.add(card.id);
                                              } else {
                                                _selectedCards.remove(card.id);
                                              }
                                            });
                                          },
                                        ),
                                        Icon(
                                          card.isUsed ? Icons.check_circle : Icons.credit_card,
                                          color: card.isUsed ? Colors.grey : Colors.green,
                                        ),
                                      ],
                                    ),
                                    title: Row(
                                      children: [
                                        Expanded(child: Text(card.id, style: const TextStyle(fontFamily: 'monospace'))),
                                        IconButton(
                                          icon: const Icon(Icons.qr_code, size: 22),
                                          tooltip: 'عرض QR',
                                          onPressed: () => _showQrDialog(card.id),
                                        ),
                                        IconButton(
                                          icon: const Icon(Icons.copy, size: 20),
                                          tooltip: 'نسخ الكود',
                                          onPressed: () async {
                                            await Clipboard.setData(ClipboardData(text: card.id));
                                            ScaffoldMessenger.of(context).showSnackBar(
                                              SnackBar(content: Text('تم نسخ الكود: ${card.id}')),
                                            );
                                          },
                                        ),
                                        PopupMenuButton<String>(
                                          onSelected: (value) {
                                            if (value == 'export') {
                                              // TODO: تنفيذ التصدير المتطور لاحقًا
                                            } else if (value == 'delete') {
                                              _deleteCard(card);
                                            }
                                          },
                                          itemBuilder: (context) => [
                                            const PopupMenuItem(value: 'export', child: Text('تصدير البطاقة')),
                                            if (!card.isUsed)
                                              const PopupMenuItem(value: 'delete', child: Text('حذف البطاقة')),
                                          ],
                                        ),
                                      ],
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text('القيمة: ${card.value}'),
                                        Text(card.isUsed
                                            ? 'مستخدمة بواسطة: ${_subscriberNames[card.usedBy] ?? card.usedBy ?? "-"}'
                                            : 'غير مستخدمة'),
                                        Text('تاريخ الإنشاء: ${card.createdAt.toString().substring(0, 16)}'),
                                        if (card.isUsed && card.usedAt != null)
                                          Text('تاريخ الاستخدام: ${card.usedAt.toString().substring(0, 16)}'),
                                      ],
                                    ),
                                    // لا حاجة لـ trailing الآن
                                  ),
                                ),
                              );
                            },
                          ),
              ),
            ],
          ),
          if (_selectedCards.isNotEmpty)
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: _BottomActionBar(
                selectedCount: _selectedCards.length,
                onDelete: _deleteSelectedCards,
                onExport: _exportSelectedCardsCsv,
                onCopy: _copySelectedCodes,
              ),
            ),
        ],
      ),
    );
  }

  // نافذة QR
  void _showQrDialog(String code) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('QR Code'),
        content: QrImageView(
          data: code,
          version: QrVersions.auto,
          size: 200.0,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  // نافذة تفاصيل البطاقة
  void _showCardDetails(ActivationCardModel card) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(child: Text('تفاصيل البطاقة', style: Theme.of(context).textTheme.titleLarge)),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Center(child: QrImageView(data: card.id, version: QrVersions.auto, size: 120)),
            const SizedBox(height: 12),
            SelectableText('الكود: ${card.id}', style: const TextStyle(fontFamily: 'monospace')),
            Text('القيمة: ${card.value}'),
            Text(card.isUsed
                ? 'مستخدمة بواسطة: ${_subscriberNames[card.usedBy] ?? card.usedBy ?? "-"}'
                : 'غير مستخدمة'),
            Text('تاريخ الإنشاء: ${card.createdAt.toString().substring(0, 16)}'),
            if (card.isUsed && card.usedAt != null)
              Text('تاريخ الاستخدام: ${card.usedAt.toString().substring(0, 16)}'),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton.icon(
                  icon: const Icon(Icons.copy),
                  label: const Text('نسخ الكود'),
                  onPressed: () async {
                    await Clipboard.setData(ClipboardData(text: card.id));
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم نسخ الكود!')),
                    );
                  },
                ),
                const SizedBox(width: 12),
                if (!card.isUsed)
                  ElevatedButton.icon(
                    icon: const Icon(Icons.delete),
                    label: const Text('حذف'),
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                    onPressed: () {
                      Navigator.of(context).pop();
                      _deleteCard(card);
                    },
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}  ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  // حذف جماعي
  Future<void> _deleteSelectedCards() async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف الجماعي'),
        content: Text('هل تريد حذف ${_selectedCards.length} بطاقة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
    if (confirm == true) {
      final batch = FirebaseFirestore.instance.batch();
      for (final id in _selectedCards) {
        batch.delete(FirebaseFirestore.instance.collection('activation_cards').doc(id));
      }
      await batch.commit();
      setState(() => _selectedCards.clear());
      _fetchInitialCards(); // تحديث
      _updateStats(); // تحديث
    }
  }

  // تصدير جماعي إلى CSV
  Future<void> _exportSelectedCardsCsv() async {
    final selected = _cards.where((c) => _selectedCards.contains(c.id)).toList();
    if (selected.isEmpty) return;
    final rows = [
      ['الكود', 'القيمة', 'الحالة', 'المستخدم', 'تاريخ الإنشاء', 'تاريخ الاستخدام'],
      ...selected.map((c) => [
        c.id,
        c.value,
        c.isUsed ? 'مستخدمة' : 'غير مستخدمة',
        c.isUsed ? (_subscriberNames[c.usedBy] ?? c.usedBy ?? '-') : '-',
        c.createdAt.toString().substring(0, 16),
        c.isUsed && c.usedAt != null ? c.usedAt.toString().substring(0, 16) : '-',
      ]),
    ];
    final csv = const ListToCsvConverter().convert(rows);
    final dir = await getTemporaryDirectory();
    final file = File('${dir.path}/activation_cards_export.csv');
    await file.writeAsString(csv);
    await Share.shareXFiles([XFile(file.path)], text: 'تصدير بطاقات التفعيل');
  }

  // نسخ جماعي للأكواد
  Future<void> _copySelectedCodes() async {
    final selected = _cards.where((c) => _selectedCards.contains(c.id)).toList();
    if (selected.isEmpty) return;
    final text = selected.map((c) => c.id).join('\n');
    await Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم نسخ الأكواد المحددة إلى الحافظة.')));
  }

  bool _isAllSelected() {
    final filtered = _filteredCards();
    return filtered.isNotEmpty && filtered.every((c) => _selectedCards.contains(c.id));
  }

  List<ActivationCardModel> _filteredCards() {
    if (filterStatus == 'all') return _cards;
    if (filterStatus == 'used') return _cards.where((c) => c.isUsed).toList();
    if (filterStatus == 'unused') return _cards.where((c) => !c.isUsed).toList();
    return _cards;
  }

  Future<void> _showSelectAllDialog() async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('تحديد الكل'),
        content: const Text('هل تريد تحديد كل الأكواد المعروضة فقط أم كل الأكواد في قاعدة البيانات حسب الفلتر؟'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _toggleSelectAll(onlyVisible: true);
            },
            child: const Text('المعروضة فقط'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _toggleSelectAll(onlyVisible: false);
            },
            child: const Text('كل الأكواد في القاعدة'),
          ),
        ],
      ),
    );
  }

  Future<void> _toggleSelectAll({required bool onlyVisible}) async {
    if (_isAllSelected()) {
      // إلغاء تحديد الكل
      setState(() {
        if (onlyVisible) {
          for (final c in _filteredCards()) {
            _selectedCards.remove(c.id);
          }
        } else {
          _selectedCards.clear();
        }
      });
      return;
    }
    if (onlyVisible) {
      setState(() {
        for (final c in _filteredCards()) {
          _selectedCards.add(c.id);
        }
      });
    } else {
      // جلب كل الأكواد من Firestore حسب الفلتر
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );
      try {
        Query query = FirebaseFirestore.instance.collection('activation_cards').where('adminId', isEqualTo: adminId);
        if (filterStatus == 'used') {
          query = query.where('isUsed', isEqualTo: true);
        } else if (filterStatus == 'unused') {
          query = query.where('isUsed', isEqualTo: false);
        }
        final snapshot = await query.get();
        setState(() {
          for (final doc in snapshot.docs) {
            _selectedCards.add(doc['id']);
          }
        });
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء تحديد الكل: $e')),
        );
      } finally {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل
      }
    }
  }
}

// إضافة ويدجت شريط الإحصائيات
class _StatCard extends StatelessWidget {
  final String label;
  final int value;
  final Color color;
  const _StatCard({required this.label, required this.value, required this.color});
  @override
  Widget build(BuildContext context) {
    return Card(
      color: color.withOpacity(0.1),
      elevation: 0,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          children: [
            Text(value.toString(), style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: color)),
            Text(label, style: TextStyle(fontSize: 13, color: color)),
          ],
        ),
      ),
    );
  }
}

// شريط الإجراءات السفلي
class _BottomActionBar extends StatelessWidget {
  final int selectedCount;
  final VoidCallback onDelete;
  final VoidCallback onExport;
  final VoidCallback onCopy;
  const _BottomActionBar({required this.selectedCount, required this.onDelete, required this.onExport, required this.onCopy});
  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 12,
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        child: Row(
          children: [
            Text('تم تحديد $selectedCount', style: const TextStyle(fontWeight: FontWeight.bold)),
            const Spacer(),
            IconButton(
              icon: const Icon(Icons.copy),
              tooltip: 'نسخ الأكواد',
              onPressed: onCopy,
            ),
            IconButton(
              icon: const Icon(Icons.file_download),
              tooltip: 'تصدير CSV',
              onPressed: onExport,
            ),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              tooltip: 'حذف جماعي',
              onPressed: onDelete,
            ),
          ],
        ),
      ),
    );
  }
} 